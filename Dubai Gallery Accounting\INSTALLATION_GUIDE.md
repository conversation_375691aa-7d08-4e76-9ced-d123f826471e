# دليل التثبيت والتشغيل - نظام محاسبة معرض دبي

## 📋 المتطلبات الأساسية

### متطلبات النظام
- **نظام التشغيل:** Windows 10/11 (64-bit)
- **المعالج:** Intel Core i3 أو أعلى
- **الذاكرة:** 4 GB RAM كحد أدنى (8 GB مُوصى به)
- **مساحة القرص:** 500 MB مساحة فارغة
- **الشاشة:** دقة 1024x768 كحد أدنى (1920x1080 مُوصى به)
- **الكاميرا:** كاميرا ويب (اختيارية لمسح QR)

### البرمجيات المطلوبة
1. **.NET 6.0 Runtime**
   - تحميل من: https://dotnet.microsoft.com/download/dotnet/6.0
   - اختر "Download .NET 6.0 Runtime" للويندوز

2. **Microsoft Visual C++ Redistributable**
   - عادة مثبت مع Windows
   - إذا لم يكن مثبت: https://aka.ms/vs/17/release/vc_redist.x64.exe

## 🚀 خطوات التثبيت

### الطريقة الأولى: التشغيل المباشر (مُوصى به)

1. **تحميل الملفات**
   ```
   قم بنسخ مجلد "Dubai Gallery Accounting" إلى:
   C:\Program Files\Dubai Gallery Accounting\
   ```

2. **تشغيل النظام**
   ```
   انقر نقراً مزدوجاً على ملف: run.bat
   ```

3. **أول تشغيل**
   - سيتم إنشاء قاعدة البيانات تلقائياً
   - بيانات تسجيل الدخول الافتراضية:
     - اسم المستخدم: `admin`
     - كلمة المرور: `admin123`

### الطريقة الثانية: البناء من المصدر

1. **تثبيت .NET SDK**
   ```
   تحميل .NET 6.0 SDK من:
   https://dotnet.microsoft.com/download/dotnet/6.0
   ```

2. **فتح موجه الأوامر**
   ```cmd
   cd "C:\Path\To\Dubai Gallery Accounting"
   ```

3. **استعادة الحزم**
   ```cmd
   dotnet restore
   ```

4. **بناء المشروع**
   ```cmd
   dotnet build --configuration Release
   ```

5. **تشغيل التطبيق**
   ```cmd
   dotnet run --configuration Release
   ```

## ⚙️ الإعداد الأولي

### 1. تسجيل الدخول الأول
- استخدم البيانات الافتراضية للدخول
- غيّر كلمة المرور فوراً من الإعدادات

### 2. إعداد معلومات المتجر
- اذهب إلى الإعدادات
- أدخل معلومات المتجر الصحيحة
- حدد العملة والضرائب

### 3. إضافة المستخدمين
- أضف البائعين والمحاسبين
- حدد الصلاحيات لكل مستخدم

### 4. إعداد الكاميرا (اختياري)
- اذهب إلى "مسح QR"
- اختبر الكاميرا المتاحة
- اضبط الإعدادات حسب الحاجة

## 📁 هيكل الملفات

```
Dubai Gallery Accounting/
├── DubaiGalleryAccounting.exe    # الملف التنفيذي
├── DubaiGallery.db              # قاعدة البيانات
├── appsettings.json             # ملف الإعدادات
├── run.bat                      # ملف التشغيل السريع
├── Backups/                     # النسخ الاحتياطية
├── Reports/                     # التقارير المُصدرة
├── Invoices/                    # الفواتير المطبوعة
├── QRCodes/                     # رموز QR المُولدة
├── Images/                      # صور المنتجات
└── Logs/                        # ملفات السجلات
```

## 🔧 استكشاف الأخطاء

### مشكلة: "لا يمكن العثور على .NET Runtime"
**الحل:**
1. تأكد من تثبيت .NET 6.0 Runtime
2. أعد تشغيل الكمبيوتر بعد التثبيت
3. جرب تشغيل الأمر: `dotnet --version`

### مشكلة: "خطأ في قاعدة البيانات"
**الحل:**
1. تأكد من أذونات الكتابة في المجلد
2. احذف ملف `DubaiGallery.db` وأعد التشغيل
3. شغل البرنامج كمدير

### مشكلة: "الكاميرا لا تعمل"
**الحل:**
1. تأكد من توصيل الكاميرا
2. أغلق البرامج الأخرى التي تستخدم الكاميرا
3. جرب كاميرا أخرى من القائمة

### مشكلة: "خطأ في الطباعة"
**الحل:**
1. تأكد من تثبيت Adobe Reader أو برنامج PDF
2. تحقق من إعدادات الطابعة
3. جرب حفظ PDF أولاً ثم الطباعة

## 🔒 الأمان والنسخ الاحتياطي

### النسخ الاحتياطي التلقائي
- يتم إنشاء نسخة احتياطية يومياً تلقائياً
- الملفات محفوظة في مجلد `Backups/`
- احتفظ بنسخة خارجية أسبوعياً

### الأمان
- غيّر كلمات المرور الافتراضية
- استخدم حساب مستخدم محدود للبائعين
- فعّل سجل النشاط

### الصيانة الدورية
- نظف ملفات السجلات شهرياً
- احذف الفواتير القديمة سنوياً
- تحقق من النسخ الاحتياطية أسبوعياً

## 📞 الدعم الفني

### في حالة المشاكل التقنية:
1. راجع هذا الدليل أولاً
2. تحقق من ملف السجلات في `Logs/app.log`
3. أعد تشغيل البرنامج
4. تواصل مع فريق الدعم

### معلومات مفيدة للدعم:
- إصدار النظام: 1.0.0
- نظام التشغيل: Windows
- إصدار .NET: 6.0
- رسالة الخطأ الكاملة
- خطوات إعادة إنتاج المشكلة

## 🎯 نصائح للاستخدام الأمثل

### للمدير:
- راجع التقارير يومياً
- تابع مستويات المخزون
- راجع سجل نشاط المستخدمين

### للبائع:
- استخدم مسح QR للسرعة
- تأكد من أسعار البيع
- احفظ الفواتير فوراً

### للمحاسب:
- صدّر التقارير دورياً
- راجع الأرباح والخسائر
- تابع المصروفات

## 📈 التحديثات المستقبلية

### الميزات المخطط لها:
- دعم عدة فروع
- تطبيق موبايل
- تكامل مع أنظمة الدفع
- تقارير متقدمة أكثر

### كيفية التحديث:
1. احتفظ بنسخة احتياطية
2. حمّل الإصدار الجديد
3. استبدل الملفات القديمة
4. شغل البرنامج للتحديث التلقائي

---

**تم إعداد هذا الدليل خصيصاً لمعرض دبي للموبايلات والكاميرات**  
*إصدار 1.0 - 2024*