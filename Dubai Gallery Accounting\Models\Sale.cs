using System.ComponentModel.DataAnnotations;

namespace DubaiGalleryAccounting.Models
{
    public class Sale
    {
        [Key]
        public int Id { get; set; }
        
        public string CustomerName { get; set; } = string.Empty;
        public string CustomerPhone { get; set; } = string.Empty;
        public string SalesmanName { get; set; } = string.Empty;
        public DateTime SaleDate { get; set; } = DateTime.Now;
        public decimal TotalAmount { get; set; }
        public decimal TotalProfit { get; set; }
        public string Notes { get; set; } = string.Empty;
        public string InvoiceNumber { get; set; } = string.Empty;
        
        public List<SaleItem> Items { get; set; } = new List<SaleItem>();
    }

    public class SaleItem
    {
        [Key]
        public int Id { get; set; }
        
        public int SaleId { get; set; }
        public Sale Sale { get; set; }
        
        public int ProductId { get; set; }
        public Product Product { get; set; }
        
        public int Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal TotalPrice { get; set; }
        public decimal Profit { get; set; }
    }
}