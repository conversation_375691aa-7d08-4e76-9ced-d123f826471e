<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="clr-namespace:DubaiGalleryAccounting.Views">

    <!-- الألوان -->
    <SolidColorBrush x:Key="PrimaryBrush" Color="#2E86AB"/>
    <SolidColorBrush x:Key="SecondaryBrush" Color="#A23B72"/>
    <SolidColorBrush x:Key="AccentBrush" Color="#F18F01"/>
    <SolidColorBrush x:Key="BackgroundBrush" Color="#F5F5F5"/>

    <!-- محول الأرباح -->
    <local:ProfitColorConverter x:Key="ProfitColorConverter"/>

    <!-- نمط النافذة -->
    <Style x:Key="MainWindowStyle" TargetType="Window">
        <Setter Property="Background" Value="{StaticResource BackgroundBrush}"/>
        <Setter Property="FontFamily" Value="Segoe UI, Tahoma"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
        <Setter Property="WindowStartupLocation" Value="CenterScreen"/>
    </Style>

    <!-- نمط الأزرار -->
    <Style x:Key="PrimaryButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Padding" Value="15,8"/>
        <Setter Property="Margin" Value="5"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Cursor" Value="Hand"/>
    </Style>

    <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource PrimaryButtonStyle}">
        <Setter Property="Background" Value="{StaticResource SecondaryBrush}"/>
    </Style>

    <!-- نمط النصوص -->
    <Style x:Key="TextBoxStyle" TargetType="TextBox">
        <Setter Property="Padding" Value="10,8"/>
        <Setter Property="Margin" Value="5"/>
        <Setter Property="BorderBrush" Value="#CCCCCC"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="FontSize" Value="14"/>
    </Style>

    <!-- نمط التسميات -->
    <Style x:Key="LabelStyle" TargetType="Label">
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Margin" Value="5,5,5,0"/>
    </Style>

    <!-- نمط الجداول -->
    <Style x:Key="DataGridStyle" TargetType="DataGrid">
        <Setter Property="AutoGenerateColumns" Value="False"/>
        <Setter Property="CanUserAddRows" Value="False"/>
        <Setter Property="IsReadOnly" Value="True"/>
        <Setter Property="GridLinesVisibility" Value="Horizontal"/>
        <Setter Property="Background" Value="White"/>
        <Setter Property="Margin" Value="10"/>
    </Style>

    <!-- نمط البطاقات -->
    <Style x:Key="CardStyle" TargetType="Border">
        <Setter Property="Background" Value="White"/>
        <Setter Property="BorderBrush" Value="#E0E0E0"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="Padding" Value="20"/>
        <Setter Property="Margin" Value="10"/>
    </Style>

    <Style x:Key="StatCardStyle" TargetType="Border" BasedOn="{StaticResource CardStyle}">
        <Setter Property="MinHeight" Value="100"/>
        <Setter Property="MinWidth" Value="200"/>
    </Style>

</ResourceDictionary>