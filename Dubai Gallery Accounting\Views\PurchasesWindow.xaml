<Window x:Class="DubaiGalleryAccounting.Views.PurchasesWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة المشتريات - معرض دبي" Height="600" Width="900"
        Style="{StaticResource MainWindowStyle}">
    <Grid>
        <DataGrid x:Name="PurchasesGrid" Style="{StaticResource DataGridStyle}">
            <DataGrid.Columns>
                <DataGridTextColumn Header="المورد" Binding="{Binding SupplierName}" Width="*"/>
                <DataGridTextColumn Header="التاريخ" Binding="{Binding PurchaseDate}" Width="150"/>
                <DataGridTextColumn Header="المبلغ" Binding="{Binding TotalAmount}" Width="100"/>
            </DataGrid.Columns>
        </DataGrid>
    </Grid>
</Window>