using System.Windows;
using System.Windows.Controls;
using DubaiGalleryAccounting.Database;
using DubaiGalleryAccounting.Models;
using DubaiGalleryAccounting.Controllers;

namespace DubaiGalleryAccounting.Views
{
    public partial class AddEditProductWindow : Window
    {
        private readonly AppDbContext _context;
        private readonly QRCodeService _qrCodeService;
        private readonly Product? _productToEdit;
        private readonly bool _isEditMode;

        public AddEditProductWindow(Product? productToEdit = null)
        {
            InitializeComponent();
            _context = new AppDbContext();
            _qrCodeService = new QRCodeService();
            _productToEdit = productToEdit;
            _isEditMode = productToEdit != null;

            if (_isEditMode)
            {
                WindowTitle.Text = "تعديل الصنف";
                SaveButton.Content = "💾 تحديث";
                LoadProductData();
            }
        }

        private void LoadProductData()
        {
            if (_productToEdit == null) return;

            NameTextBox.Text = _productToEdit.Name;
            TypeComboBox.Text = _productToEdit.Type.ToString();
            BrandComboBox.Text = _productToEdit.Brand;
            ModelTextBox.Text = _productToEdit.Model;
            IMEITextBox.Text = _productToEdit.IMEI;
            PurchasePriceTextBox.Text = _productToEdit.PurchasePrice.ToString("F2");
            SalePriceTextBox.Text = _productToEdit.SalePrice.ToString("F2");
            QuantityTextBox.Text = _productToEdit.Quantity.ToString();
            NotesTextBox.Text = _productToEdit.Notes;
            IsActiveCheckBox.IsChecked = _productToEdit.IsActive;
            GenerateQRCheckBox.IsChecked = false; // لا نريد إعادة توليد QR في التعديل

            UpdateProfitCalculation();
        }

        private void PriceTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            UpdateProfitCalculation();
        }

        private void UpdateProfitCalculation()
        {
            if (decimal.TryParse(PurchasePriceTextBox.Text, out decimal purchasePrice) &&
                decimal.TryParse(SalePriceTextBox.Text, out decimal salePrice))
            {
                var profit = salePrice - purchasePrice;
                ProfitText.Text = $"الربح المتوقع: {profit:F2} ريال";
                
                if (profit < 0)
                {
                    ProfitText.Foreground = System.Windows.Media.Brushes.Red;
                }
                else if (profit > 0)
                {
                    ProfitText.Foreground = System.Windows.Media.Brushes.Green;
                }
                else
                {
                    ProfitText.Foreground = System.Windows.Media.Brushes.Black;
                }
            }
            else
            {
                ProfitText.Text = "الربح المتوقع: 0.00 ريال";
                ProfitText.Foreground = System.Windows.Media.Brushes.Black;
            }
        }

        private void ScanIMEIButton_Click(object sender, RoutedEventArgs e)
        {
            var qrScanWindow = new QRScanWindow();
            if (qrScanWindow.ShowDialog() == true && !string.IsNullOrEmpty(qrScanWindow.ScannedData))
            {
                // استخراج IMEI من البيانات المقروءة
                var scannedData = qrScanWindow.ScannedData;
                if (scannedData.Contains("IMEI:"))
                {
                    var imeiStart = scannedData.IndexOf("IMEI:") + 5;
                    var imeiEnd = scannedData.IndexOf("|", imeiStart);
                    if (imeiEnd == -1) imeiEnd = scannedData.Length;
                    
                    var imei = scannedData.Substring(imeiStart, imeiEnd - imeiStart);
                    IMEITextBox.Text = imei;
                }
                else
                {
                    IMEITextBox.Text = scannedData;
                }
            }
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                Product product;
                
                if (_isEditMode)
                {
                    product = _productToEdit!;
                }
                else
                {
                    // التحقق من عدم تكرار IMEI
                    var existingProduct = await _context.Products
                        .FirstOrDefaultAsync(p => p.IMEI == IMEITextBox.Text.Trim());
                    
                    if (existingProduct != null)
                    {
                        ShowValidationError("رقم IMEI موجود مسبقاً");
                        return;
                    }

                    product = new Product();
                    _context.Products.Add(product);
                }

                // تحديث بيانات المنتج
                product.Name = NameTextBox.Text.Trim();
                product.Type = Enum.Parse<ProductType>(TypeComboBox.Text.Replace(" ", "_"));
                product.Brand = BrandComboBox.Text.Trim();
                product.Model = ModelTextBox.Text.Trim();
                product.IMEI = IMEITextBox.Text.Trim();
                product.PurchasePrice = decimal.Parse(PurchasePriceTextBox.Text);
                product.SalePrice = decimal.Parse(SalePriceTextBox.Text);
                product.Quantity = int.Parse(QuantityTextBox.Text);
                product.Notes = NotesTextBox.Text.Trim();
                product.IsActive = IsActiveCheckBox.IsChecked ?? true;
                product.LastModified = DateTime.Now;

                // توليد QR Code إذا كان مطلوباً
                if (GenerateQRCheckBox.IsChecked == true)
                {
                    try
                    {
                        var qrPath = _qrCodeService.GenerateQRCode(product.IMEI, product.Name);
                        product.QRCodePath = qrPath;
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"تم حفظ المنتج ولكن فشل في توليد QR: {ex.Message}", 
                            "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                }

                await _context.SaveChangesAsync();

                // تسجيل النشاط
                var action = _isEditMode ? "تعديل منتج" : "إضافة منتج";
                _context.ActivityLogs.Add(new ActivityLog
                {
                    UserId = LoginWindow.CurrentUser?.Id ?? 1,
                    Action = action,
                    Details = $"{action}: {product.Name} (IMEI: {product.IMEI})",
                    ActionDate = DateTime.Now
                });
                await _context.SaveChangesAsync();

                MessageBox.Show($"تم {(_isEditMode ? "تحديث" : "حفظ")} المنتج بنجاح", "نجح", 
                    MessageBoxButton.OK, MessageBoxImage.Information);

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                ShowValidationError($"خطأ في حفظ المنتج: {ex.Message}");
            }
        }

        private bool ValidateInput()
        {
            // التحقق من الحقول المطلوبة
            if (string.IsNullOrWhiteSpace(NameTextBox.Text))
            {
                ShowValidationError("يرجى إدخال اسم الصنف");
                NameTextBox.Focus();
                return false;
            }

            if (TypeComboBox.SelectedIndex == -1)
            {
                ShowValidationError("يرجى اختيار نوع الصنف");
                TypeComboBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(IMEITextBox.Text))
            {
                ShowValidationError("يرجى إدخال رقم IMEI/Serial");
                IMEITextBox.Focus();
                return false;
            }

            if (!decimal.TryParse(PurchasePriceTextBox.Text, out decimal purchasePrice) || purchasePrice < 0)
            {
                ShowValidationError("يرجى إدخال سعر شراء صحيح");
                PurchasePriceTextBox.Focus();
                return false;
            }

            if (!decimal.TryParse(SalePriceTextBox.Text, out decimal salePrice) || salePrice < 0)
            {
                ShowValidationError("يرجى إدخال سعر بيع صحيح");
                SalePriceTextBox.Focus();
                return false;
            }

            if (!int.TryParse(QuantityTextBox.Text, out int quantity) || quantity < 0)
            {
                ShowValidationError("يرجى إدخال كمية صحيحة");
                QuantityTextBox.Focus();
                return false;
            }

            // تحذير إذا كان سعر البيع أقل من سعر الشراء
            if (salePrice < purchasePrice)
            {
                var result = MessageBox.Show("سعر البيع أقل من سعر الشراء. هل تريد المتابعة؟", 
                    "تحذير", MessageBoxButton.YesNo, MessageBoxImage.Warning);
                
                if (result == MessageBoxResult.No)
                    return false;
            }

            return true;
        }

        private void ShowValidationError(string message)
        {
            ValidationMessage.Text = message;
            ValidationMessage.Visibility = Visibility.Visible;
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        protected override void OnClosed(EventArgs e)
        {
            _context?.Dispose();
            base.OnClosed(e);
        }
    }
}