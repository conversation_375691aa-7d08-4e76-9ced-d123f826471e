using System.Windows;
using System.Windows.Controls;
using System.Globalization;
using DubaiGalleryAccounting.Database;
using DubaiGalleryAccounting.Models;
using DubaiGalleryAccounting.Controllers;
using Microsoft.EntityFrameworkCore;

namespace DubaiGalleryAccounting.Views
{
    public partial class SalesWindow : Window
    {
        private readonly AppDbContext _context;
        private readonly InvoiceService _invoiceService;
        private Sale? _selectedSale;

        public SalesWindow()
        {
            InitializeComponent();
            _context = new AppDbContext();
            _invoiceService = new InvoiceService(new QRCodeService());
            FromDatePicker.SelectedDate = DateTime.Now.AddMonths(-1);
            ToDatePicker.SelectedDate = DateTime.Now;
            LoadSales();
        }

        private async void LoadSales()
        {
            try
            {
                var fromDate = FromDatePicker.SelectedDate ?? DateTime.Now.AddMonths(-1);
                var toDate = ToDatePicker.SelectedDate ?? DateTime.Now;
                var sales = await _context.Sales
                    .Where(s => s.SaleDate >= fromDate && s.SaleDate <= toDate.AddDays(1))
                    .Include(s => s.Items).ThenInclude(i => i.Product)
                    .OrderByDescending(s => s.SaleDate).ToListAsync();
                SalesDataGrid.ItemsSource = sales;
                UpdateStatistics(sales);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المبيعات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateStatistics(List<Sale> sales)
        {
            var totalAmount = sales.Sum(s => s.TotalAmount);
            var totalProfit = sales.Sum(s => s.TotalProfit);
            var salesCount = sales.Count;
            var averageSale = salesCount > 0 ? totalAmount / salesCount : 0;

            TotalSalesAmountText.Text = $"{totalAmount:F2} ريال";
            TotalProfitAmountText.Text = $"{totalProfit:F2} ريال";
            TotalSalesCountText.Text = salesCount.ToString();
            AverageSaleText.Text = $"{averageSale:F2} ريال";

            TotalProfitAmountText.Foreground = totalProfit > 0 ? System.Windows.Media.Brushes.Green : 
                                             totalProfit < 0 ? System.Windows.Media.Brushes.Red : 
                                             System.Windows.Media.Brushes.Black;
        }

        private void SalesDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            _selectedSale = SalesDataGrid.SelectedItem as Sale;
        }

        private void NewSaleButton_Click(object sender, RoutedEventArgs e)
        {
            var newSaleWindow = new NewSaleWindow();
            if (newSaleWindow.ShowDialog() == true) LoadSales();
        }

        private void ViewSaleButton_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedSale == null)
            {
                MessageBox.Show("يرجى اختيار فاتورة للعرض", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }
            var viewSaleWindow = new ViewSaleWindow(_selectedSale);
            viewSaleWindow.ShowDialog();
        }

        private async void PrintSaleButton_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedSale == null)
            {
                MessageBox.Show("يرجى اختيار فاتورة للطباعة", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }
            try
            {
                var saleWithDetails = await _context.Sales.Include(s => s.Items).ThenInclude(i => i.Product)
                    .FirstOrDefaultAsync(s => s.Id == _selectedSale.Id);
                if (saleWithDetails == null) return;
                var pdfPath = _invoiceService.GenerateInvoice(saleWithDetails);
                System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo { FileName = pdfPath, UseShellExecute = true });
                MessageBox.Show("تم إنشاء الفاتورة بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة الفاتورة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e) => LoadSales();
        private void DatePicker_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
        {
            if (FromDatePicker.SelectedDate.HasValue && ToDatePicker.SelectedDate.HasValue) LoadSales();
        }

        protected override void OnClosed(EventArgs e)
        {
            _context?.Dispose();
            base.OnClosed(e);
        }
    }

    public class ProfitColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is decimal profit)
            {
                if (profit > 0) return "Positive";
                if (profit < 0) return "Negative";
            }
            return "Neutral";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}