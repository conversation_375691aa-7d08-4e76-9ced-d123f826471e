using QRCoder;
using System.Drawing;
using System.Drawing.Imaging;

namespace DubaiGalleryAccounting.Controllers
{
    public class QRCodeService
    {
        private readonly string _qrCodesPath;

        public QRCodeService()
        {
            _qrCodesPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "QRCodes");
            if (!Directory.Exists(_qrCodesPath))
                Directory.CreateDirectory(_qrCodesPath);
        }

        public string GenerateQRCode(string imei, string productName)
        {
            try
            {
                var qrGenerator = new QRCodeGenerator();
                var qrData = $"IMEI:{imei}|Product:{productName}|Store:معرض دبي";
                var qrCodeData = qrGenerator.CreateQrCode(qrData, QRCodeGenerator.ECCLevel.Q);
                var qrCode = new QRCode(qrCodeData);
                
                using (var qrCodeImage = qrCode.GetGraphic(20))
                {
                    var fileName = $"QR_{imei}_{DateTime.Now:yyyyMMddHHmmss}.png";
                    var filePath = Path.Combine(_qrCodesPath, fileName);
                    
                    qrCodeImage.Save(filePath, ImageFormat.Png);
                    return filePath;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في توليد رمز QR: {ex.Message}");
            }
        }
    }
}