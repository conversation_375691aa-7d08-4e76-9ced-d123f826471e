@echo off
chcp 65001 >nul
title معرض دبي - برنامج التثبيت

echo ========================================
echo    معرض دبي للموبايلات والكاميرات
echo        برنامج التثبيت التلقائي
echo ========================================
echo.

REM التحقق من صلاحيات المدير
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo تحذير: يُنصح بتشغيل البرنامج كمدير للحصول على أفضل أداء
    echo.
)

REM إنشاء المجلدات المطلوبة
echo [1/6] إنشاء المجلدات...
if not exist "Backups" mkdir "Backups"
if not exist "Reports" mkdir "Reports"
if not exist "Invoices" mkdir "Invoices"
if not exist "QRCodes" mkdir "QRCodes"
if not exist "Images" mkdir "Images"
if not exist "Logs" mkdir "Logs"
echo ✓ تم إنشاء المجلدات بنجاح

REM التحقق من .NET
echo.
echo [2/6] التحقق من .NET 6.0...
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ .NET 6.0 غير مثبت
    echo يرجى تحميل وتثبيت .NET 6.0 من:
    echo https://dotnet.microsoft.com/download/dotnet/6.0
    echo.
    echo اضغط أي مفتاح للمتابعة بعد التثبيت...
    pause >nul
    
    REM إعادة التحقق
    dotnet --version >nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ لا يزال .NET غير مثبت. يرجى التثبيت والمحاولة مرة أخرى
        pause
        exit /b 1
    )
)
echo ✓ .NET 6.0 مثبت بنجاح

REM استعادة الحزم
echo.
echo [3/6] تحميل المكتبات المطلوبة...
dotnet restore >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ فشل في تحميل المكتبات
    echo جاري المحاولة مرة أخرى...
    dotnet restore
    if %errorlevel% neq 0 (
        echo ❌ فشل في تحميل المكتبات. تحقق من الاتصال بالإنترنت
        pause
        exit /b 1
    )
)
echo ✓ تم تحميل المكتبات بنجاح

REM بناء المشروع
echo.
echo [4/6] بناء البرنامج...
dotnet build --configuration Release --verbosity quiet
if %errorlevel% neq 0 (
    echo ❌ فشل في بناء البرنامج
    echo جاري عرض التفاصيل...
    dotnet build --configuration Release
    pause
    exit /b 1
)
echo ✓ تم بناء البرنامج بنجاح

REM إنشاء قاعدة البيانات
echo.
echo [5/6] إعداد قاعدة البيانات...
if not exist "DubaiGallery.db" (
    echo جاري إنشاء قاعدة البيانات...
    dotnet run --configuration Release --no-build -- --setup-db >nul 2>&1
)
echo ✓ قاعدة البيانات جاهزة

REM إنشاء اختصارات
echo.
echo [6/6] إنشاء الاختصارات...

REM إنشاء ملف تشغيل محسن
echo @echo off > "تشغيل معرض دبي.bat"
echo chcp 65001 ^>nul >> "تشغيل معرض دبي.bat"
echo title معرض دبي - نظام المحاسبة >> "تشغيل معرض دبي.bat"
echo cd /d "%%~dp0" >> "تشغيل معرض دبي.bat"
echo echo جاري تشغيل معرض دبي... >> "تشغيل معرض دبي.bat"
echo dotnet run --configuration Release --no-build >> "تشغيل معرض دبي.bat"
echo if %%errorlevel%% neq 0 pause >> "تشغيل معرض دبي.bat"

echo ✓ تم إنشاء الاختصارات

echo.
echo ========================================
echo           تم التثبيت بنجاح! 🎉
echo ========================================
echo.
echo بيانات تسجيل الدخول الافتراضية:
echo المستخدم: admin
echo كلمة المرور: admin123
echo.
echo لتشغيل البرنامج:
echo 1. انقر مزدوج على "تشغيل معرض دبي.bat"
echo 2. أو استخدم الأمر: dotnet run
echo.
echo ملاحظات مهمة:
echo • غيّر كلمة المرور الافتراضية فوراً
echo • راجع ملف README.md للتعليمات الكاملة
echo • النسخ الاحتياطية في مجلد Backups
echo.
echo هل تريد تشغيل البرنامج الآن؟ (Y/N)
set /p choice=
if /i "%choice%"=="Y" (
    echo.
    echo جاري تشغيل معرض دبي...
    start "" "تشغيل معرض دبي.bat"
) else (
    echo.
    echo يمكنك تشغيل البرنامج لاحقاً من "تشغيل معرض دبي.bat"
)

echo.
echo شكراً لاستخدام نظام محاسبة معرض دبي!
echo.
pause