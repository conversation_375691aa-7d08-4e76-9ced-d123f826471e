﻿#pragma checksum "..\..\..\..\Views\AddEditProductWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "62C125C75DA38A082240EBDB493D2D330808A373"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DubaiGalleryAccounting.Views {
    
    
    /// <summary>
    /// AddEditProductWindow
    /// </summary>
    public partial class AddEditProductWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 13 "..\..\..\..\Views\AddEditProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WindowTitle;
        
        #line default
        #line hidden
        
        
        #line 23 "..\..\..\..\Views\AddEditProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NameTextBox;
        
        #line default
        #line hidden
        
        
        #line 27 "..\..\..\..\Views\AddEditProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 38 "..\..\..\..\Views\AddEditProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox BrandComboBox;
        
        #line default
        #line hidden
        
        
        #line 53 "..\..\..\..\Views\AddEditProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ModelTextBox;
        
        #line default
        #line hidden
        
        
        #line 62 "..\..\..\..\Views\AddEditProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox IMEITextBox;
        
        #line default
        #line hidden
        
        
        #line 82 "..\..\..\..\Views\AddEditProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PurchasePriceTextBox;
        
        #line default
        #line hidden
        
        
        #line 89 "..\..\..\..\Views\AddEditProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SalePriceTextBox;
        
        #line default
        #line hidden
        
        
        #line 96 "..\..\..\..\Views\AddEditProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProfitText;
        
        #line default
        #line hidden
        
        
        #line 104 "..\..\..\..\Views\AddEditProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox QuantityTextBox;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\..\..\Views\AddEditProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NotesTextBox;
        
        #line default
        #line hidden
        
        
        #line 117 "..\..\..\..\Views\AddEditProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox GenerateQRCheckBox;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\..\Views\AddEditProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IsActiveCheckBox;
        
        #line default
        #line hidden
        
        
        #line 131 "..\..\..\..\Views\AddEditProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\..\Views\AddEditProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ValidationMessage;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DubaiGalleryAccounting;component/views/addeditproductwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\AddEditProductWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.WindowTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.NameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.TypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 4:
            this.BrandComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 5:
            this.ModelTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.IMEITextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            
            #line 70 "..\..\..\..\Views\AddEditProductWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ScanIMEIButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.PurchasePriceTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 84 "..\..\..\..\Views\AddEditProductWindow.xaml"
            this.PurchasePriceTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.PriceTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 9:
            this.SalePriceTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 91 "..\..\..\..\Views\AddEditProductWindow.xaml"
            this.SalePriceTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.PriceTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 10:
            this.ProfitText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.QuantityTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.NotesTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 13:
            this.GenerateQRCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 14:
            this.IsActiveCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 15:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 135 "..\..\..\..\Views\AddEditProductWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            
            #line 139 "..\..\..\..\Views\AddEditProductWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.ValidationMessage = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

