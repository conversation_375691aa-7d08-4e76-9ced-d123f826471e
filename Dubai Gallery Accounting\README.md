# نظام محاسبة معرض دبي للموبايلات والكاميرات

## 🏪 نظرة عامة
نظام محاسبة شامل ومتطور مصمم خصيصاً لمعرض دبي للموبايلات والكاميرات، يدعم اللغة العربية بالكامل مع واجهة من اليمين إلى اليسار (RTL).

## ✨ الميزات الرئيسية

### 📦 إدارة الأصناف
- تصنيف المنتجات (موبايلات، كاميرات، إكسسوارات)
- توليد رموز QR تلقائياً لكل منتج
- إدارة الصور والملاحظات
- تتبع المخزون والكميات

### 💰 إدارة المبيعات والمشتريات
- فواتير مبيعات احترافية مع QR لكل منتج
- حساب الأرباح الفوري مع التنبيهات الذكية
- مسح QR بالكاميرا للبيع السريع
- طباعة الفواتير بصيغة PDF

### 📊 التقارير والإحصائيات
- لوحة معلومات تفاعلية
- تقارير يومية وشهرية وسنوية
- رسوم بيانية للمبيعات والأرباح
- تصدير التقارير إلى Excel وPDF

### 🔐 إدارة المستخدمين
- صلاحيات متعددة (مدير، بائع، محاسب)
- سجل نشاط المستخدمين
- تسجيل دخول آمن

### 🤖 الميزات الذكية
- تنبيهات المخزون المنخفض
- تحذيرات البيع بخسارة
- نسخ احتياطي تلقائي
- بحث ذكي متقدم

## 🛠️ المتطلبات التقنية

### البرمجيات المطلوبة
- Windows 10/11
- .NET 6.0 Runtime
- كاميرا ويب (اختيارية لمسح QR)

### المكتبات المستخدمة
```xml
- Microsoft.EntityFrameworkCore.Sqlite (قاعدة البيانات)
- QRCoder (توليد رموز QR)
- ZXing.Net (مسح رموز QR)
- iTextSharp (إنشاء ملفات PDF)
- LiveCharts.Wpf (الرسوم البيانية)
- AForge.Video (دعم الكاميرا)
- ClosedXML (تصدير Excel)
```

## 🚀 التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd "Dubai Gallery Accounting"
```

### 2. بناء المشروع
```bash
dotnet restore
dotnet build
```

### 3. تشغيل التطبيق
```bash
dotnet run
```

### 4. بيانات تسجيل الدخول الافتراضية
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

## 📁 هيكل المشروع

```
Dubai Gallery Accounting/
├── Models/                 # نماذج البيانات
│   ├── Product.cs         # نموذج المنتج
│   ├── Sale.cs           # نموذج المبيعات
│   ├── Purchase.cs       # نموذج المشتريات
│   ├── Expense.cs        # نموذج المصروفات
│   └── User.cs           # نموذج المستخدم
├── Views/                 # واجهات المستخدم
│   ├── MainWindow.xaml   # النافذة الرئيسية
│   ├── ProductsWindow.xaml # إدارة الأصناف
│   ├── SalesWindow.xaml  # إدارة المبيعات
│   └── QRScanWindow.xaml # مسح QR
├── Controllers/           # منطق العمل
│   ├── QRCodeService.cs  # خدمة QR
│   ├── InvoiceService.cs # خدمة الفواتير
│   └── ReportService.cs  # خدمة التقارير
├── Database/             # قاعدة البيانات
│   └── AppDbContext.cs   # سياق قاعدة البيانات
└── Resources/            # الموارد والأنماط
    └── Styles.xaml       # أنماط الواجهة
```

## 🎯 كيفية الاستخدام

### إضافة منتج جديد
1. اذهب إلى "إدارة الأصناف"
2. اضغط "إضافة صنف جديد"
3. املأ البيانات المطلوبة
4. سيتم توليد QR تلقائياً

### إجراء عملية بيع
1. اذهب إلى "المبيعات"
2. اضغط "فاتورة جديدة"
3. أضف المنتجات (يدوياً أو بمسح QR)
4. احفظ واطبع الفاتورة

### مسح QR Code
1. اذهب إلى "مسح QR"
2. شغل الكاميرا أو حمل صورة
3. وجه الكاميرا نحو الرمز
4. استخدم البيانات المقروءة

## 📊 لوحة المعلومات

تعرض لوحة المعلومات:
- إجمالي المبيعات والأرباح
- عدد المنتجات منخفضة المخزون
- أعلى المنتجات مبيعاً
- رسم بياني للمبيعات الشهرية

## 🔧 الإعدادات المتقدمة

### تخصيص التنبيهات
- حد المخزون المنخفض (افتراضي: 5)
- تنبيهات البيع بخسارة
- تكرار النسخ الاحتياطي

### إدارة المستخدمين
- إضافة مستخدمين جدد
- تعديل الصلاحيات
- مراجعة سجل النشاط

## 📄 التقارير المتاحة

### تقارير المبيعات
- تقرير يومي/شهري/سنوي
- تفصيل الأرباح والخسائر
- أداء البائعين

### تقارير المخزون
- حالة المخزون الحالية
- المنتجات منخفضة المخزون
- حركة المخزون

### التصدير
- Excel (.xlsx)
- PDF
- طباعة مباشرة

## 🛡️ الأمان والنسخ الاحتياطي

### النسخ الاحتياطي
- نسخ تلقائي يومي لقاعدة البيانات
- حفظ في مجلد منفصل
- إمكانية الاستعادة السريعة

### الأمان
- تشفير كلمات المرور
- سجل نشاط شامل
- صلاحيات محددة لكل مستخدم

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها

**الكاميرا لا تعمل:**
- تأكد من توصيل الكاميرا
- تحقق من أذونات الكاميرا
- جرب كاميرا أخرى من القائمة

**خطأ في قاعدة البيانات:**
- تأكد من وجود ملف DubaiGallery.db
- تحقق من أذونات الكتابة
- أعد تشغيل التطبيق

**مشاكل الطباعة:**
- تأكد من تثبيت Adobe Reader
- تحقق من إعدادات الطابعة
- جرب حفظ PDF أولاً

## 📞 الدعم والمساعدة

للحصول على المساعدة:
1. راجع هذا الدليل أولاً
2. تحقق من سجل الأخطاء
3. تواصل مع فريق الدعم

## 📝 ملاحظات مهمة

- **النسخ الاحتياطي:** يُنصح بعمل نسخة احتياطية يدوية أسبوعياً
- **التحديثات:** تحقق من التحديثات شهرياً
- **الأداء:** نظف قاعدة البيانات كل 6 أشهر
- **الأمان:** غير كلمات المرور دورياً

## 🎉 شكر خاص

تم تطوير هذا النظام خصيصاً لمعرض دبي للموبايلات والكاميرات بأحدث التقنيات وأفضل الممارسات في البرمجة.

---

**معرض دبي للموبايلات والكاميرات**  
*نظام محاسبة احترافي - 2024*