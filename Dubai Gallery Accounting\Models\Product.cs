using System.ComponentModel.DataAnnotations;

namespace DubaiGalleryAccounting.Models
{
    public class Product
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        public string Name { get; set; } = string.Empty;
        
        [Required]
        public ProductType Type { get; set; }
        
        public string Brand { get; set; } = string.Empty;
        public string Model { get; set; } = string.Empty;
        
        [Required]
        public string IMEI { get; set; } = string.Empty;
        
        public decimal PurchasePrice { get; set; }
        public decimal SalePrice { get; set; }
        public int Quantity { get; set; }
        public string ImagePath { get; set; } = string.Empty;
        public string QRCodePath { get; set; } = string.Empty;
        public string Notes { get; set; } = string.Empty;
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime? LastModified { get; set; }
        public bool IsActive { get; set; } = true;
    }

    public enum ProductType
    {
        موبايل = 1,
        كاميرا = 2,
        إكسسوارات_موبايل = 3,
        إكسسوارات_كاميرا = 4
    }
}