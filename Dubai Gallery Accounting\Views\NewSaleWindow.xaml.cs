using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using DubaiGalleryAccounting.Database;
using DubaiGalleryAccounting.Models;
using DubaiGalleryAccounting.Controllers;
using Microsoft.EntityFrameworkCore;

namespace DubaiGalleryAccounting.Views
{
    public partial class NewSaleWindow : Window
    {
        private readonly AppDbContext _context;
        private readonly InvoiceService _invoiceService;
        private readonly ObservableCollection<SaleItemViewModel> _saleItems;
        private List<Product> _availableProducts;

        public NewSaleWindow()
        {
            InitializeComponent();
            _context = new AppDbContext();
            _invoiceService = new InvoiceService();
            _saleItems = new ObservableCollection<SaleItemViewModel>();
            _availableProducts = new List<Product>();
            InitializeWindow();
        }

        private async void InitializeWindow()
        {
            InvoiceNumberTextBox.Text = $"INV-{DateTime.Now:yyyyMMddHHmmss}";
            SaleDatePicker.SelectedDate = DateTime.Now;
            SalesmanComboBox.ItemsSource = new[] { "البائع الافتراضي", "أحمد محمد", "فاطمة علي" };
            SalesmanComboBox.SelectedIndex = 0;
            await LoadAvailableProducts();
            SaleItemsDataGrid.ItemsSource = _saleItems;
            CustomerNameTextBox.Focus();
        }

        private async Task LoadAvailableProducts()
        {
            try
            {
                _availableProducts = await _context.Products
                    .Where(p => p.IsActive && p.Quantity > 0)
                    .OrderBy(p => p.Name).ToListAsync();

                var productDisplayList = _availableProducts.Select(p => new
                {
                    Id = p.Id,
                    DisplayText = $"{p.Name} - {p.Brand} (متاح: {p.Quantity}) - {p.SalePrice:F2} ريال"
                }).ToList();

                ProductComboBox.ItemsSource = productDisplayList;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المنتجات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ProductComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (ProductComboBox.SelectedValue != null)
            {
                var productId = (int)ProductComboBox.SelectedValue;
                var product = _availableProducts.FirstOrDefault(p => p.Id == productId);
                if (product != null)
                {
                    UnitPriceTextBox.Text = product.SalePrice.ToString("F2");
                    QuantityTextBox.Focus();
                }
            }
        }

        private void QuantityTextBox_TextChanged(object sender, TextChangedEventArgs e) => UpdateItemCalculation();
        private void UnitPriceTextBox_TextChanged(object sender, TextChangedEventArgs e) => UpdateItemCalculation();

        private void UpdateItemCalculation()
        {
            if (int.TryParse(QuantityTextBox.Text, out int quantity) &&
                decimal.TryParse(UnitPriceTextBox.Text, out decimal price) &&
                ProductComboBox.SelectedValue != null)
            {
                var productId = (int)ProductComboBox.SelectedValue;
                var product = _availableProducts.FirstOrDefault(p => p.Id == productId);
                if (product != null)
                {
                    var profit = (price - product.PurchasePrice) * quantity;
                    UnitPriceTextBox.Background = profit < 0 ? System.Windows.Media.Brushes.LightPink : System.Windows.Media.Brushes.White;
                }
            }
        }

        private void AddItemButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (ProductComboBox.SelectedValue == null)
                {
                    MessageBox.Show("يرجى اختيار منتج", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (!int.TryParse(QuantityTextBox.Text, out int quantity) || quantity <= 0)
                {
                    MessageBox.Show("يرجى إدخال كمية صحيحة", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (!decimal.TryParse(UnitPriceTextBox.Text, out decimal unitPrice) || unitPrice <= 0)
                {
                    MessageBox.Show("يرجى إدخال سعر صحيح", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var productId = (int)ProductComboBox.SelectedValue;
                var product = _availableProducts.FirstOrDefault(p => p.Id == productId);

                if (quantity > product.Quantity)
                {
                    MessageBox.Show($"الكمية المطلوبة ({quantity}) أكبر من المتاح ({product.Quantity})", 
                        "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var profit = (unitPrice - product.PurchasePrice) * quantity;
                if (profit < 0)
                {
                    var result = MessageBox.Show($"تحذير: هذا البيع سيحقق خسارة قدرها {Math.Abs(profit):F2} ريال. هل تريد المتابعة؟", 
                        "تحذير خسارة", MessageBoxButton.YesNo, MessageBoxImage.Warning);
                    if (result == MessageBoxResult.No) return;
                }

                var saleItem = new SaleItemViewModel
                {
                    ProductId = productId,
                    ProductName = product.Name,
                    ProductIMEI = product.IMEI,
                    Quantity = quantity,
                    UnitPrice = unitPrice,
                    TotalPrice = quantity * unitPrice,
                    Profit = profit,
                    PurchasePrice = product.PurchasePrice
                };

                _saleItems.Add(saleItem);

                ProductComboBox.SelectedIndex = -1;
                QuantityTextBox.Text = "1";
                UnitPriceTextBox.Text = "";
                UnitPriceTextBox.Background = System.Windows.Media.Brushes.White;
                UpdateTotals();
                ProductComboBox.Focus();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة الصنف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateTotals()
        {
            var itemsCount = _saleItems.Sum(i => i.Quantity);
            var totalAmount = _saleItems.Sum(i => i.TotalPrice);
            var totalProfit = _saleItems.Sum(i => i.Profit);

            ItemsCountText.Text = itemsCount.ToString();
            TotalAmountText.Text = $"{totalAmount:F2} ريال";
            TotalProfitText.Text = $"{totalProfit:F2} ريال";

            TotalProfitText.Foreground = totalProfit > 0 ? System.Windows.Media.Brushes.Green :
                                        totalProfit < 0 ? System.Windows.Media.Brushes.Red :
                                        System.Windows.Media.Brushes.Black;
        }

        private void ScanQRButton_Click(object sender, RoutedEventArgs e)
        {
            var qrScanWindow = new QRScanWindow();
            if (qrScanWindow.ShowDialog() == true && !string.IsNullOrEmpty(qrScanWindow.ScannedData))
            {
                var imei = qrScanWindow.ScannedData;
                var product = _availableProducts.FirstOrDefault(p => p.IMEI.Contains(imei) || imei.Contains(p.IMEI));
                if (product != null)
                {
                    ProductComboBox.SelectedValue = product.Id;
                    UnitPriceTextBox.Text = product.SalePrice.ToString("F2");
                    QuantityTextBox.Focus();
                }
                else
                {
                    MessageBox.Show("لم يتم العثور على منتج بهذا الرمز", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }

        private async void SaveSaleButton_Click(object sender, RoutedEventArgs e) => await SaveSale(false);
        private async void SaveAndPrintButton_Click(object sender, RoutedEventArgs e) => await SaveSale(true);

        private async Task SaveSale(bool printAfterSave)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(CustomerNameTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم العميل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (_saleItems.Count == 0)
                {
                    MessageBox.Show("يرجى إضافة أصناف للفاتورة", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var sale = new Sale
                {
                    InvoiceNumber = InvoiceNumberTextBox.Text,
                    CustomerName = CustomerNameTextBox.Text.Trim(),
                    CustomerPhone = CustomerPhoneTextBox.Text.Trim(),
                    SalesmanName = SalesmanComboBox.Text,
                    SaleDate = SaleDatePicker.SelectedDate ?? DateTime.Now,
                    TotalAmount = _saleItems.Sum(i => i.TotalPrice),
                    TotalProfit = _saleItems.Sum(i => i.Profit)
                };

                _context.Sales.Add(sale);
                await _context.SaveChangesAsync();

                foreach (var item in _saleItems)
                {
                    var saleItem = new SaleItem
                    {
                        SaleId = sale.Id,
                        ProductId = item.ProductId,
                        Quantity = item.Quantity,
                        UnitPrice = item.UnitPrice,
                        TotalPrice = item.TotalPrice,
                        Profit = item.Profit
                    };

                    _context.SaleItems.Add(saleItem);

                    var product = await _context.Products.FindAsync(item.ProductId);
                    if (product != null)
                    {
                        product.Quantity -= item.Quantity;
                        product.LastModified = DateTime.Now;
                    }
                }

                await _context.SaveChangesAsync();

                MessageBox.Show("تم حفظ الفاتورة بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);

                if (printAfterSave)
                {
                    try
                    {
                        var saleWithItems = await _context.Sales.Include(s => s.Items).ThenInclude(i => i.Product)
                            .FirstOrDefaultAsync(s => s.Id == sale.Id);
                        var filePath = _invoiceService.GenerateInvoice(saleWithItems);
                        System.Diagnostics.Process.Start("notepad.exe", filePath);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"تم حفظ الفاتورة ولكن فشلت الطباعة: {ex.Message}", 
                            "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                }

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الفاتورة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل تريد إلغاء الفاتورة؟", "تأكيد الإلغاء", MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (result == MessageBoxResult.Yes)
            {
                DialogResult = false;
                Close();
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            _context?.Dispose();
            base.OnClosed(e);
        }
    }

    public class SaleItemViewModel
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public string ProductIMEI { get; set; } = string.Empty;
        public int Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal TotalPrice { get; set; }
        public decimal Profit { get; set; }
        public decimal PurchasePrice { get; set; }
        public bool IsProfit => Profit >= 0;
    }
}