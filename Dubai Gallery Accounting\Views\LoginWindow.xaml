<Window x:Class="DubaiGalleryAccounting.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تسجيل الدخول - معرض دبي" 
        Height="400" Width="500"
        Style="{StaticResource MainWindowStyle}"
        ResizeMode="NoResize"
        WindowStartupLocation="CenterScreen">
    
    <Grid Background="{StaticResource BackgroundBrush}">
        <Border Style="{StaticResource CardStyle}" 
                HorizontalAlignment="Center" 
                VerticalAlignment="Center"
                Width="400" Height="300">
            <StackPanel>
                <!-- شعار المعرض -->
                <TextBlock Text="معرض دبي" 
                          FontSize="28" 
                          FontWeight="Bold" 
                          Foreground="{StaticResource PrimaryBrush}"
                          HorizontalAlignment="Center" 
                          Margin="0,0,0,10"/>
                
                <TextBlock Text="للموبايلات والكاميرات" 
                          FontSize="16" 
                          Foreground="{StaticResource SecondaryBrush}"
                          HorizontalAlignment="Center" 
                          Margin="0,0,0,30"/>

                <!-- حقول تسجيل الدخول -->
                <Label Content="اسم المستخدم:" Style="{StaticResource LabelStyle}"/>
                <TextBox x:Name="UsernameTextBox" 
                        Style="{StaticResource TextBoxStyle}"
                        Text="admin"/>

                <Label Content="كلمة المرور:" Style="{StaticResource LabelStyle}"/>
                <PasswordBox x:Name="PasswordBox" 
                            Margin="5" 
                            Padding="10,8"
                            BorderBrush="#CCCCCC"
                            BorderThickness="1"
                            FontSize="14"/>

                <!-- رسالة الخطأ -->
                <TextBlock x:Name="ErrorMessage" 
                          Foreground="Red" 
                          HorizontalAlignment="Center"
                          Margin="0,10,0,0"
                          Visibility="Collapsed"/>

                <!-- أزرار -->
                <StackPanel Orientation="Horizontal" 
                           HorizontalAlignment="Center" 
                           Margin="0,20,0,0">
                    <Button Content="تسجيل الدخول" 
                           Style="{StaticResource PrimaryButtonStyle}"
                           Width="120"
                           Click="LoginButton_Click"/>
                    <Button Content="إلغاء" 
                           Style="{StaticResource SecondaryButtonStyle}"
                           Width="80"
                           Click="CancelButton_Click"/>
                </StackPanel>
            </StackPanel>
        </Border>
    </Grid>
</Window>