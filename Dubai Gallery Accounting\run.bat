@echo off
echo ========================================
echo    معرض دبي - نظام المحاسبة
echo ========================================
echo.
echo جاري تشغيل النظام...
echo.

REM التحقق من وجود .NET 6
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: .NET 6.0 غير مثبت على النظام
    echo يرجى تحميل وتثبيت .NET 6.0 من:
    echo https://dotnet.microsoft.com/download/dotnet/6.0
    pause
    exit /b 1
)

REM بناء المشروع
echo جاري بناء المشروع...
dotnet build --configuration Release

if %errorlevel% neq 0 (
    echo خطأ في بناء المشروع
    pause
    exit /b 1
)

REM تشغيل التطبيق
echo تم بناء المشروع بنجاح
echo جاري تشغيل التطبيق...
echo.
dotnet run --configuration Release

if %errorlevel% neq 0 (
    echo خطأ في تشغيل التطبيق
    pause
    exit /b 1
)

echo.
echo تم إغلاق التطبيق
pause