using DubaiGalleryAccounting.Models;

namespace DubaiGalleryAccounting.Controllers
{
    public class InvoiceService
    {
        private readonly string _invoicesPath;

        public InvoiceService()
        {
            _invoicesPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Invoices");
            if (!Directory.Exists(_invoicesPath))
                Directory.CreateDirectory(_invoicesPath);
        }

        public string GenerateInvoice(Sale sale)
        {
            try
            {
                var fileName = $"Invoice_{sale.InvoiceNumber}_{DateTime.Now:yyyyMMddHHmmss}.txt";
                var filePath = Path.Combine(_invoicesPath, fileName);

                var content = $@"
معرض دبي للموبايلات والكاميرات
=====================================

رقم الفاتورة: {sale.InvoiceNumber}
التاريخ: {sale.SaleDate:yyyy/MM/dd}
العميل: {sale.CustomerName}
الهاتف: {sale.CustomerPhone}
البائع: {sale.SalesmanName}

الأصناف:
---------
";

                foreach (var item in sale.Items)
                {
                    content += $"{item.Product.Name} - الكمية: {item.Quantity} - السعر: {item.UnitPrice:F2} - المجموع: {item.TotalPrice:F2}\n";
                }

                content += $@"
---------
المجموع الكلي: {sale.TotalAmount:F2} ريال
الربح: {sale.TotalProfit:F2} ريال

شكراً لتعاملكم معنا
";

                File.WriteAllText(filePath, content, System.Text.Encoding.UTF8);
                return filePath;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء الفاتورة: {ex.Message}");
            }
        }
    }
}