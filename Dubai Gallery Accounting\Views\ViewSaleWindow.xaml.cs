using System.Windows;
using DubaiGalleryAccounting.Models;

namespace DubaiGalleryAccounting.Views
{
    public partial class ViewSaleWindow : Window
    {
        public ViewSaleWindow(Sale sale)
        {
            InitializeComponent();
            InvoiceTitle.Text = $"فاتورة رقم: {sale.InvoiceNumber} - العميل: {sale.CustomerName}";
            ItemsGrid.ItemsSource = sale.Items;
            TotalText.Text = $"المجموع الكلي: {sale.TotalAmount:F2} ريال";
        }
    }
}