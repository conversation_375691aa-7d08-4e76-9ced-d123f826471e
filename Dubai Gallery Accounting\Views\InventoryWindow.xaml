<Window x:Class="DubaiGalleryAccounting.Views.InventoryWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة المخزن - معرض دبي" Height="600" Width="900"
        Style="{StaticResource MainWindowStyle}">
    <Grid>
        <DataGrid x:Name="InventoryGrid" Style="{StaticResource DataGridStyle}">
            <DataGrid.Columns>
                <DataGridTextColumn Header="المنتج" Binding="{Binding Name}" Width="*"/>
                <DataGridTextColumn Header="الكمية" Binding="{Binding Quantity}" Width="100"/>
                <DataGridTextColumn Header="السعر" Binding="{Binding SalePrice}" Width="100"/>
            </DataGrid.Columns>
        </DataGrid>
    </Grid>
</Window>