using System.Windows;
using DubaiGalleryAccounting.Database;
using Microsoft.EntityFrameworkCore;

namespace DubaiGalleryAccounting.Views
{
    public partial class ExpensesWindow : Window
    {
        public ExpensesWindow()
        {
            InitializeComponent();
            LoadExpenses();
        }

        private async void LoadExpenses()
        {
            using var context = new AppDbContext();
            var expenses = await context.Expenses.ToListAsync();
            ExpensesGrid.ItemsSource = expenses;
        }
    }
}