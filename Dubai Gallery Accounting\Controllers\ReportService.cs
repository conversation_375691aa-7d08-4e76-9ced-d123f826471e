using DubaiGalleryAccounting.Database;
using DubaiGalleryAccounting.Models;
using Microsoft.EntityFrameworkCore;
using ClosedXML.Excel;

namespace DubaiGalleryAccounting.Controllers
{
    public class ReportService
    {
        private readonly AppDbContext _context;

        public ReportService(AppDbContext context)
        {
            _context = context;
        }

        public class DashboardData
        {
            public decimal TotalSales { get; set; }
            public decimal TotalPurchases { get; set; }
            public decimal TotalExpenses { get; set; }
            public decimal TotalProfit { get; set; }
            public int TotalSalesCount { get; set; }
            public int LowStockCount { get; set; }
            public List<TopSellingProduct> TopProducts { get; set; } = new();
            public List<MonthlySales> MonthlySalesData { get; set; } = new();
        }

        public class TopSellingProduct
        {
            public string ProductName { get; set; } = string.Empty;
            public int QuantitySold { get; set; }
            public decimal TotalRevenue { get; set; }
        }

        public class MonthlySales
        {
            public string Month { get; set; } = string.Empty;
            public decimal Sales { get; set; }
            public decimal Profit { get; set; }
        }

        public async Task<DashboardData> GetDashboardDataAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            fromDate ??= DateTime.Now.AddMonths(-12);
            toDate ??= DateTime.Now;

            var sales = await _context.Sales
                .Where(s => s.SaleDate >= fromDate && s.SaleDate <= toDate)
                .Include(s => s.Items)
                .ThenInclude(i => i.Product)
                .ToListAsync();

            var purchases = await _context.Purchases
                .Where(p => p.PurchaseDate >= fromDate && p.PurchaseDate <= toDate)
                .ToListAsync();

            var expenses = await _context.Expenses
                .Where(e => e.ExpenseDate >= fromDate && e.ExpenseDate <= toDate)
                .ToListAsync();

            var lowStockProducts = await _context.Products
                .Where(p => p.Quantity <= 5 && p.IsActive)
                .CountAsync();

            var topProducts = sales
                .SelectMany(s => s.Items)
                .GroupBy(i => i.Product.Name)
                .Select(g => new TopSellingProduct
                {
                    ProductName = g.Key,
                    QuantitySold = g.Sum(i => i.Quantity),
                    TotalRevenue = g.Sum(i => i.TotalPrice)
                })
                .OrderByDescending(p => p.QuantitySold)
                .Take(10)
                .ToList();

            var monthlySales = sales
                .GroupBy(s => s.SaleDate.ToString("yyyy-MM"))
                .Select(g => new MonthlySales
                {
                    Month = g.Key,
                    Sales = g.Sum(s => s.TotalAmount),
                    Profit = g.Sum(s => s.TotalProfit)
                })
                .OrderBy(m => m.Month)
                .ToList();

            return new DashboardData
            {
                TotalSales = sales.Sum(s => s.TotalAmount),
                TotalPurchases = purchases.Sum(p => p.TotalAmount),
                TotalExpenses = expenses.Sum(e => e.Amount),
                TotalProfit = sales.Sum(s => s.TotalProfit),
                TotalSalesCount = sales.Count,
                LowStockCount = lowStockProducts,
                TopProducts = topProducts,
                MonthlySalesData = monthlySales
            };
        }

        public async Task<string> ExportSalesReportAsync(DateTime fromDate, DateTime toDate)
        {
            var sales = await _context.Sales
                .Where(s => s.SaleDate >= fromDate && s.SaleDate <= toDate)
                .Include(s => s.Items)
                .ThenInclude(i => i.Product)
                .OrderByDescending(s => s.SaleDate)
                .ToListAsync();

            using var workbook = new XLWorkbook();
            var worksheet = workbook.Worksheets.Add("تقرير المبيعات");

            // إعداد الرؤوس
            worksheet.Cell(1, 1).Value = "رقم الفاتورة";
            worksheet.Cell(1, 2).Value = "التاريخ";
            worksheet.Cell(1, 3).Value = "العميل";
            worksheet.Cell(1, 4).Value = "البائع";
            worksheet.Cell(1, 5).Value = "المجموع";
            worksheet.Cell(1, 6).Value = "الربح";

            // تنسيق الرؤوس
            var headerRange = worksheet.Range(1, 1, 1, 6);
            headerRange.Style.Font.Bold = true;
            headerRange.Style.Fill.BackgroundColor = XLColor.LightBlue;

            // إضافة البيانات
            int row = 2;
            foreach (var sale in sales)
            {
                worksheet.Cell(row, 1).Value = sale.InvoiceNumber;
                worksheet.Cell(row, 2).Value = sale.SaleDate.ToString("yyyy/MM/dd");
                worksheet.Cell(row, 3).Value = sale.CustomerName;
                worksheet.Cell(row, 4).Value = sale.SalesmanName;
                worksheet.Cell(row, 5).Value = sale.TotalAmount;
                worksheet.Cell(row, 6).Value = sale.TotalProfit;
                row++;
            }

            // تنسيق الأعمدة
            worksheet.Columns().AdjustToContents();

            var fileName = $"Sales_Report_{fromDate:yyyyMMdd}_{toDate:yyyyMMdd}.xlsx";
            var filePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Reports", fileName);
            
            Directory.CreateDirectory(Path.GetDirectoryName(filePath));
            workbook.SaveAs(filePath);

            return filePath;
        }

        public async Task<List<Product>> GetLowStockProductsAsync(int threshold = 5)
        {
            return await _context.Products
                .Where(p => p.Quantity <= threshold && p.IsActive)
                .OrderBy(p => p.Quantity)
                .ToListAsync();
        }

        public async Task<decimal> CalculateProfitAsync(DateTime fromDate, DateTime toDate)
        {
            var totalSales = await _context.Sales
                .Where(s => s.SaleDate >= fromDate && s.SaleDate <= toDate)
                .SumAsync(s => s.TotalProfit);

            var totalExpenses = await _context.Expenses
                .Where(e => e.ExpenseDate >= fromDate && e.ExpenseDate <= toDate)
                .SumAsync(e => e.Amount);

            return totalSales - totalExpenses;
        }
    }
}