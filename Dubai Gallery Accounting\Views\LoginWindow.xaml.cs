using System.Windows;
using DubaiGalleryAccounting.Database;
using DubaiGalleryAccounting.Models;
using Microsoft.EntityFrameworkCore;

namespace DubaiGalleryAccounting.Views
{
    public partial class LoginWindow : Window
    {
        public static User? CurrentUser { get; private set; }

        public LoginWindow()
        {
            InitializeComponent();
            PasswordBox.Password = "admin123"; // للاختبار فقط
        }

        private async void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ErrorMessage.Visibility = Visibility.Collapsed;
                
                var username = UsernameTextBox.Text.Trim();
                var password = PasswordBox.Password;

                if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
                {
                    ShowError("يرجى إدخال اسم المستخدم وكلمة المرور");
                    return;
                }

                using var context = new AppDbContext();
                var user = await context.Users
                    .FirstOrDefaultAsync(u => u.Username == username && u.Password == password && u.IsActive);

                if (user == null)
                {
                    ShowError("اسم المستخدم أو كلمة المرور غير صحيحة");
                    return;
                }

                // تحديث آخر تسجيل دخول
                user.LastLogin = DateTime.Now;
                await context.SaveChangesAsync();

                // تسجيل النشاط
                context.ActivityLogs.Add(new ActivityLog
                {
                    UserId = user.Id,
                    Action = "تسجيل دخول",
                    Details = $"تم تسجيل الدخول بنجاح",
                    ActionDate = DateTime.Now
                });
                await context.SaveChangesAsync();

                CurrentUser = user;

                // فتح النافذة الرئيسية
                var mainWindow = new MainWindow();
                mainWindow.Show();
                this.Close();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تسجيل الدخول: {ex.Message}");
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            Application.Current.Shutdown();
        }

        private void ShowError(string message)
        {
            ErrorMessage.Text = message;
            ErrorMessage.Visibility = Visibility.Visible;
        }
    }
}