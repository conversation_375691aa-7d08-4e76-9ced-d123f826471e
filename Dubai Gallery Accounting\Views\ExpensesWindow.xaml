<Window x:Class="DubaiGalleryAccounting.Views.ExpensesWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة المصروفات - معرض دبي" Height="600" Width="900"
        Style="{StaticResource MainWindowStyle}">
    <Grid>
        <DataGrid x:Name="ExpensesGrid" Style="{StaticResource DataGridStyle}">
            <DataGrid.Columns>
                <DataGridTextColumn Header="النوع" Binding="{Binding Type}" Width="150"/>
                <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="*"/>
                <DataGridTextColumn Header="المبلغ" Binding="{Binding Amount}" Width="100"/>
                <DataGridTextColumn Header="التاريخ" Binding="{Binding ExpenseDate}" Width="150"/>
            </DataGrid.Columns>
        </DataGrid>
    </Grid>
</Window>