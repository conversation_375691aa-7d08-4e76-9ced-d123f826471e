using AForge.Video;
using AForge.Video.DirectShow;
using ZXing;
using ZXing.Windows.Compatibility;
using System.Drawing;

namespace DubaiGalleryAccounting.Controllers
{
    public class QRScannerService
    {
        private VideoCaptureDevice _videoSource;
        private BarcodeReader _barcodeReader;
        public event Action<string> QRCodeScanned;
        public event Action<Bitmap> FrameCaptured;

        public QRScannerService()
        {
            _barcodeReader = new BarcodeReader();
        }

        public List<string> GetAvailableCameras()
        {
            var cameras = new List<string>();
            var videoDevices = new FilterInfoCollection(FilterCategory.VideoInputDevice);
            
            foreach (FilterInfo device in videoDevices)
            {
                cameras.Add(device.Name);
            }
            
            return cameras;
        }

        public void StartScanning(int cameraIndex = 0)
        {
            try
            {
                var videoDevices = new FilterInfoCollection(FilterCategory.VideoInputDevice);
                
                if (videoDevices.Count == 0)
                    throw new Exception("لم يتم العثور على كاميرا");

                if (cameraIndex >= videoDevices.Count)
                    cameraIndex = 0;

                _videoSource = new VideoCaptureDevice(videoDevices[cameraIndex].MonikerString);
                _videoSource.NewFrame += VideoSource_NewFrame;
                _videoSource.Start();
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تشغيل الكاميرا: {ex.Message}");
            }
        }

        private void VideoSource_NewFrame(object sender, NewFrameEventArgs eventArgs)
        {
            var frame = (Bitmap)eventArgs.Frame.Clone();
            FrameCaptured?.Invoke(frame);

            try
            {
                var result = _barcodeReader.Decode(frame);
                if (result != null)
                {
                    QRCodeScanned?.Invoke(result.Text);
                }
            }
            catch
            {
                // تجاهل أخطاء القراءة
            }
        }

        public void StopScanning()
        {
            if (_videoSource != null && _videoSource.IsRunning)
            {
                _videoSource.SignalToStop();
                _videoSource.WaitForStop();
                _videoSource = null;
            }
        }

        public string ScanQRFromImage(string imagePath)
        {
            try
            {
                using (var bitmap = new Bitmap(imagePath))
                {
                    var result = _barcodeReader.Decode(bitmap);
                    return result?.Text;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في قراءة رمز QR من الصورة: {ex.Message}");
            }
        }

        public void Dispose()
        {
            StopScanning();
            _barcodeReader?.Dispose();
        }
    }
}