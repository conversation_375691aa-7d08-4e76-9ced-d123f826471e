<Window x:Class="DubaiGalleryAccounting.Views.AddEditProductWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة/تعديل صنف - معرض دبي" 
        Height="600" Width="500"
        Style="{StaticResource MainWindowStyle}"
        ResizeMode="NoResize"
        WindowStartupLocation="CenterOwner">

    <ScrollViewer>
        <Border Style="{StaticResource CardStyle}" Margin="20">
            <StackPanel>
                <TextBlock x:Name="WindowTitle" 
                          Text="إضافة صنف جديد" 
                          FontSize="18" 
                          FontWeight="Bold" 
                          Foreground="{StaticResource PrimaryBrush}"
                          HorizontalAlignment="Center" 
                          Margin="0,0,0,20"/>

                <!-- اسم الصنف -->
                <Label Content="اسم الصنف:" Style="{StaticResource LabelStyle}"/>
                <TextBox x:Name="NameTextBox" Style="{StaticResource TextBoxStyle}"/>

                <!-- النوع -->
                <Label Content="النوع:" Style="{StaticResource LabelStyle}"/>
                <ComboBox x:Name="TypeComboBox" 
                         Style="{StaticResource TextBoxStyle}"
                         Height="35">
                    <ComboBoxItem Content="موبايل"/>
                    <ComboBoxItem Content="كاميرا"/>
                    <ComboBoxItem Content="إكسسوارات موبايل"/>
                    <ComboBoxItem Content="إكسسوارات كاميرا"/>
                </ComboBox>

                <!-- الماركة -->
                <Label Content="الماركة:" Style="{StaticResource LabelStyle}"/>
                <ComboBox x:Name="BrandComboBox" 
                         Style="{StaticResource TextBoxStyle}"
                         Height="35"
                         IsEditable="True">
                    <ComboBoxItem Content="Samsung"/>
                    <ComboBoxItem Content="Apple"/>
                    <ComboBoxItem Content="Huawei"/>
                    <ComboBoxItem Content="Xiaomi"/>
                    <ComboBoxItem Content="Canon"/>
                    <ComboBoxItem Content="Nikon"/>
                    <ComboBoxItem Content="Sony"/>
                </ComboBox>

                <!-- الموديل -->
                <Label Content="الموديل:" Style="{StaticResource LabelStyle}"/>
                <TextBox x:Name="ModelTextBox" Style="{StaticResource TextBoxStyle}"/>

                <!-- IMEI/Serial -->
                <Label Content="رقم IMEI/Serial:" Style="{StaticResource LabelStyle}"/>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <TextBox x:Name="IMEITextBox" 
                            Grid.Column="0"
                            Style="{StaticResource TextBoxStyle}"
                            Margin="5,5,0,5"/>
                    <Button Grid.Column="1" 
                           Content="📷 مسح"
                           Style="{StaticResource SecondaryButtonStyle}"
                           Width="60"
                           Click="ScanIMEIButton_Click"/>
                </Grid>

                <!-- الأسعار -->
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0" Margin="0,0,5,0">
                        <Label Content="سعر الشراء:" Style="{StaticResource LabelStyle}"/>
                        <TextBox x:Name="PurchasePriceTextBox" 
                                Style="{StaticResource TextBoxStyle}"
                                TextChanged="PriceTextBox_TextChanged"/>
                    </StackPanel>

                    <StackPanel Grid.Column="1" Margin="5,0,0,0">
                        <Label Content="سعر البيع:" Style="{StaticResource LabelStyle}"/>
                        <TextBox x:Name="SalePriceTextBox" 
                                Style="{StaticResource TextBoxStyle}"
                                TextChanged="PriceTextBox_TextChanged"/>
                    </StackPanel>
                </Grid>

                <!-- الربح المتوقع -->
                <TextBlock x:Name="ProfitText" 
                          Text="الربح المتوقع: 0.00 ريال"
                          FontWeight="Bold"
                          HorizontalAlignment="Center"
                          Margin="0,5,0,10"/>

                <!-- الكمية -->
                <Label Content="الكمية:" Style="{StaticResource LabelStyle}"/>
                <TextBox x:Name="QuantityTextBox" 
                        Style="{StaticResource TextBoxStyle}"
                        Text="1"/>

                <!-- الملاحظات -->
                <Label Content="الملاحظات:" Style="{StaticResource LabelStyle}"/>
                <TextBox x:Name="NotesTextBox" 
                        Style="{StaticResource TextBoxStyle}"
                        Height="80" 
                        TextWrapping="Wrap" 
                        AcceptsReturn="True"/>

                <!-- خيارات إضافية -->
                <CheckBox x:Name="GenerateQRCheckBox" 
                         Content="توليد رمز QR تلقائياً" 
                         IsChecked="True"
                         Margin="5,10,5,5"/>

                <CheckBox x:Name="IsActiveCheckBox" 
                         Content="نشط" 
                         IsChecked="True"
                         Margin="5"/>

                <!-- أزرار التحكم -->
                <StackPanel Orientation="Horizontal" 
                           HorizontalAlignment="Center" 
                           Margin="0,20,0,0">
                    <Button x:Name="SaveButton"
                           Content="💾 حفظ" 
                           Style="{StaticResource PrimaryButtonStyle}"
                           Width="100"
                           Click="SaveButton_Click"/>
                    <Button Content="❌ إلغاء" 
                           Style="{StaticResource SecondaryButtonStyle}"
                           Width="80"
                           Click="CancelButton_Click"/>
                </StackPanel>

                <!-- رسالة التحقق -->
                <TextBlock x:Name="ValidationMessage" 
                          Foreground="Red" 
                          HorizontalAlignment="Center"
                          Margin="0,10,0,0"
                          TextWrapping="Wrap"
                          Visibility="Collapsed"/>
            </StackPanel>
        </Border>
    </ScrollViewer>
</Window>