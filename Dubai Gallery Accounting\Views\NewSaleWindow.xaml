<Window x:Class="DubaiGalleryAccounting.Views.NewSaleWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="فاتورة بيع جديدة - معرض دبي" 
        Height="700" Width="1000"
        Style="{StaticResource MainWindowStyle}"
        WindowStartupLocation="CenterOwner">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- معلومات الفاتورة -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <Label Content="رقم الفاتورة:" Style="{StaticResource LabelStyle}"/>
                    <TextBox x:Name="InvoiceNumberTextBox" 
                            Style="{StaticResource TextBoxStyle}"
                            IsReadOnly="True"/>
                    
                    <Label Content="اسم العميل:" Style="{StaticResource LabelStyle}"/>
                    <TextBox x:Name="CustomerNameTextBox" Style="{StaticResource TextBoxStyle}"/>
                </StackPanel>

                <StackPanel Grid.Column="1">
                    <Label Content="التاريخ:" Style="{StaticResource LabelStyle}"/>
                    <DatePicker x:Name="SaleDatePicker" 
                               Style="{StaticResource TextBoxStyle}"
                               Height="35"/>
                    
                    <Label Content="رقم الهاتف:" Style="{StaticResource LabelStyle}"/>
                    <TextBox x:Name="CustomerPhoneTextBox" Style="{StaticResource TextBoxStyle}"/>
                </StackPanel>

                <StackPanel Grid.Column="2">
                    <Label Content="البائع:" Style="{StaticResource LabelStyle}"/>
                    <ComboBox x:Name="SalesmanComboBox" 
                             Style="{StaticResource TextBoxStyle}"
                             Height="35"
                             IsEditable="True"/>
                    
                    <Button Content="📷 مسح QR لإضافة منتج" 
                           Style="{StaticResource PrimaryButtonStyle}"
                           Click="ScanQRButton_Click"
                           Margin="5,20,5,5"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- شريط إضافة المنتجات -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <Label Content="اختيار المنتج:" Style="{StaticResource LabelStyle}"/>
                    <ComboBox x:Name="ProductComboBox" 
                             Style="{StaticResource TextBoxStyle}"
                             Height="35"
                             DisplayMemberPath="DisplayText"
                             SelectedValuePath="Id"
                             SelectionChanged="ProductComboBox_SelectionChanged"/>
                </StackPanel>

                <StackPanel Grid.Column="1">
                    <Label Content="الكمية:" Style="{StaticResource LabelStyle}"/>
                    <TextBox x:Name="QuantityTextBox" 
                            Style="{StaticResource TextBoxStyle}"
                            Text="1"
                            TextChanged="QuantityTextBox_TextChanged"/>
                </StackPanel>

                <StackPanel Grid.Column="2">
                    <Label Content="السعر:" Style="{StaticResource LabelStyle}"/>
                    <TextBox x:Name="UnitPriceTextBox" 
                            Style="{StaticResource TextBoxStyle}"
                            TextChanged="UnitPriceTextBox_TextChanged"/>
                </StackPanel>

                <StackPanel Grid.Column="3" VerticalAlignment="Bottom">
                    <Button Content="➕ إضافة" 
                           Style="{StaticResource PrimaryButtonStyle}"
                           Click="AddItemButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- جدول أصناف الفاتورة -->
        <DataGrid Grid.Row="2" 
                 x:Name="SaleItemsDataGrid"
                 Style="{StaticResource DataGridStyle}"
                 CanUserDeleteRows="True"
                 KeyDown="SaleItemsDataGrid_KeyDown">
            <DataGrid.Columns>
                <DataGridTextColumn Header="اسم المنتج" Binding="{Binding ProductName}" Width="*" IsReadOnly="True"/>
                <DataGridTextColumn Header="IMEI" Binding="{Binding ProductIMEI}" Width="120" IsReadOnly="True"/>
                <DataGridTextColumn Header="الكمية" Binding="{Binding Quantity}" Width="80"/>
                <DataGridTextColumn Header="السعر" Binding="{Binding UnitPrice, StringFormat='{}{0:F2}'}" Width="100"/>
                <DataGridTextColumn Header="المجموع" Binding="{Binding TotalPrice, StringFormat='{}{0:F2}'}" Width="100" IsReadOnly="True"/>
                <DataGridTextColumn Header="الربح" Width="100" IsReadOnly="True">
                    <DataGridTextColumn.Binding>
                        <Binding Path="Profit" StringFormat="{}{0:F2}"/>
                    </DataGridTextColumn.Binding>
                    <DataGridTextColumn.ElementStyle>
                        <Style TargetType="TextBlock">
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsProfit}" Value="True">
                                    <Setter Property="Foreground" Value="Green"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                </DataTrigger>
                                <DataTrigger Binding="{Binding IsProfit}" Value="False">
                                    <Setter Property="Foreground" Value="Red"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </DataGridTextColumn.ElementStyle>
                </DataGridTextColumn>
            </DataGrid.Columns>
        </DataGrid>

        <!-- المجاميع والأزرار -->
        <Border Grid.Row="3" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- المجاميع -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" HorizontalAlignment="Left">
                    <TextBlock Text="عدد الأصناف: " FontWeight="Bold" VerticalAlignment="Center"/>
                    <TextBlock x:Name="ItemsCountText" Text="0" VerticalAlignment="Center" Margin="0,0,20,0"/>
                    
                    <TextBlock Text="المجموع الكلي: " FontWeight="Bold" VerticalAlignment="Center"/>
                    <TextBlock x:Name="TotalAmountText" Text="0.00 ريال" FontWeight="Bold" FontSize="16" VerticalAlignment="Center" Margin="0,0,20,0"/>
                    
                    <TextBlock Text="إجمالي الربح: " FontWeight="Bold" VerticalAlignment="Center"/>
                    <TextBlock x:Name="TotalProfitText" Text="0.00 ريال" FontWeight="Bold" FontSize="16" VerticalAlignment="Center"/>
                </StackPanel>

                <!-- الأزرار -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="💾 حفظ الفاتورة" 
                           Style="{StaticResource PrimaryButtonStyle}"
                           Click="SaveSaleButton_Click"/>
                    <Button Content="🖨️ حفظ وطباعة" 
                           Style="{StaticResource SecondaryButtonStyle}"
                           Click="SaveAndPrintButton_Click"/>
                    <Button Content="❌ إلغاء" 
                           Style="{StaticResource SecondaryButtonStyle}"
                           Click="CancelButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>