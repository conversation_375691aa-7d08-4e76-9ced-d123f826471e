<Window x:Class="DubaiGalleryAccounting.Views.SalesWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة المبيعات - معرض دبي" 
        Height="700" Width="1200"
        Style="{StaticResource MainWindowStyle}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- شريط الأدوات -->
        <Border Grid.Row="0" Background="{StaticResource PrimaryBrush}" Padding="10">
            <StackPanel Orientation="Horizontal">
                <Button Content="➕ فاتورة جديدة" 
                       Style="{StaticResource SecondaryButtonStyle}"
                       Click="NewSaleButton_Click"/>
                <Button Content="👁️ عرض الفاتورة" 
                       Style="{StaticResource SecondaryButtonStyle}"
                       Click="ViewSaleButton_Click"/>
                <Button Content="🖨️ طباعة" 
                       Style="{StaticResource SecondaryButtonStyle}"
                       Click="PrintSaleButton_Click"/>
                <Button Content="🔄 تحديث" 
                       Style="{StaticResource SecondaryButtonStyle}"
                       Click="RefreshButton_Click"/>
                <Separator Margin="10,0"/>
                <DatePicker x:Name="FromDatePicker" 
                           Width="120" 
                           Height="30"
                           SelectedDateChanged="DatePicker_SelectedDateChanged"/>
                <TextBlock Text="إلى" VerticalAlignment="Center" Margin="5,0" Foreground="White"/>
                <DatePicker x:Name="ToDatePicker" 
                           Width="120" 
                           Height="30"
                           SelectedDateChanged="DatePicker_SelectedDateChanged"/>
            </StackPanel>
        </Border>

        <!-- المحتوى الرئيسي -->
        <Grid Grid.Row="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- إحصائيات سريعة -->
            <UniformGrid Grid.Row="0" Columns="4" Margin="10">
                <Border Style="{StaticResource StatCardStyle}">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="💰" FontSize="24" HorizontalAlignment="Center"/>
                        <TextBlock x:Name="TotalSalesAmountText" 
                                  Text="0.00 ريال" 
                                  FontSize="16" 
                                  FontWeight="Bold" 
                                  HorizontalAlignment="Center"/>
                        <TextBlock Text="إجمالي المبيعات" 
                                  FontSize="10" 
                                  Foreground="Gray" 
                                  HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <Border Style="{StaticResource StatCardStyle}">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="📈" FontSize="24" HorizontalAlignment="Center"/>
                        <TextBlock x:Name="TotalProfitAmountText" 
                                  Text="0.00 ريال" 
                                  FontSize="16" 
                                  FontWeight="Bold" 
                                  HorizontalAlignment="Center"/>
                        <TextBlock Text="إجمالي الأرباح" 
                                  FontSize="10" 
                                  Foreground="Gray" 
                                  HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <Border Style="{StaticResource StatCardStyle}">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="🛒" FontSize="24" HorizontalAlignment="Center"/>
                        <TextBlock x:Name="TotalSalesCountText" 
                                  Text="0" 
                                  FontSize="16" 
                                  FontWeight="Bold" 
                                  HorizontalAlignment="Center"/>
                        <TextBlock Text="عدد الفواتير" 
                                  FontSize="10" 
                                  Foreground="Gray" 
                                  HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <Border Style="{StaticResource StatCardStyle}">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="📊" FontSize="24" HorizontalAlignment="Center"/>
                        <TextBlock x:Name="AverageSaleText" 
                                  Text="0.00 ريال" 
                                  FontSize="16" 
                                  FontWeight="Bold" 
                                  HorizontalAlignment="Center"/>
                        <TextBlock Text="متوسط الفاتورة" 
                                  FontSize="10" 
                                  Foreground="Gray" 
                                  HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>
            </UniformGrid>

            <!-- جدول المبيعات -->
            <DataGrid Grid.Row="1" 
                     x:Name="SalesDataGrid"
                     Style="{StaticResource DataGridStyle}"
                     SelectionChanged="SalesDataGrid_SelectionChanged">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="رقم الفاتورة" Binding="{Binding InvoiceNumber}" Width="120"/>
                    <DataGridTextColumn Header="التاريخ" Binding="{Binding SaleDate, StringFormat='{}{0:yyyy/MM/dd HH:mm}'}" Width="140"/>
                    <DataGridTextColumn Header="العميل" Binding="{Binding CustomerName}" Width="150"/>
                    <DataGridTextColumn Header="الهاتف" Binding="{Binding CustomerPhone}" Width="120"/>
                    <DataGridTextColumn Header="البائع" Binding="{Binding SalesmanName}" Width="120"/>
                    <DataGridTextColumn Header="المجموع" Binding="{Binding TotalAmount, StringFormat='{}{0:F2} ريال'}" Width="100"/>
                    <DataGridTextColumn Header="الربح" Width="100">
                        <DataGridTextColumn.Binding>
                            <Binding Path="TotalProfit" StringFormat="{}{0:F2} ريال"/>
                        </DataGridTextColumn.Binding>
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding TotalProfit, Converter={StaticResource ProfitColorConverter}}" Value="Positive">
                                        <Setter Property="Foreground" Value="Green"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding TotalProfit, Converter={StaticResource ProfitColorConverter}}" Value="Negative">
                                        <Setter Property="Foreground" Value="Red"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="عدد الأصناف" Binding="{Binding Items.Count}" Width="100"/>
                </DataGrid.Columns>
            </DataGrid>
        </Grid>
    </Grid>
</Window>