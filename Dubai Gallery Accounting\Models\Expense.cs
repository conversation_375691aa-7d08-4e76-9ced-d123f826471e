using System.ComponentModel.DataAnnotations;

namespace DubaiGalleryAccounting.Models
{
    public class Expense
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        public ExpenseType Type { get; set; }
        
        public string Description { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public DateTime ExpenseDate { get; set; } = DateTime.Now;
        public string Notes { get; set; } = string.Empty;
        public string CreatedBy { get; set; } = string.Empty;
    }

    public enum ExpenseType
    {
        إيجار = 1,
        كهرباء = 2,
        ماء = 3,
        صيانة = 4,
        راتب = 5,
        مواصلات = 6,
        أخرى = 7
    }
}