{"AppSettings": {"StoreName": "معرض دبي للموبايلات والكاميرات", "StoreNameEnglish": "Dubai Gallery for Mobiles and Cameras", "Version": "1.0.0", "DatabasePath": "DubaiGallery.db", "BackupPath": "Backups", "ReportsPath": "Reports", "InvoicesPath": "Invoices", "QRCodesPath": "QRCodes", "ImagesPath": "Images"}, "BusinessSettings": {"Currency": "ريال سعودي", "CurrencySymbol": "ريال", "TaxRate": 0.15, "LowStockThreshold": 5, "AutoBackupEnabled": true, "AutoBackupIntervalHours": 24, "DefaultSalesman": "البائع الافتراضي"}, "UISettings": {"Language": "ar-SA", "FlowDirection": "RightToLeft", "Theme": "<PERSON><PERSON><PERSON>", "FontFamily": "Segoe UI, Tahoma", "FontSize": 14, "ShowSplashScreen": true, "RememberWindowSize": true}, "SecuritySettings": {"SessionTimeoutMinutes": 480, "MaxLoginAttempts": 3, "PasswordMinLength": 6, "RequireStrongPassword": false, "LogUserActivity": true, "AutoLogoutEnabled": false}, "PrintSettings": {"DefaultPrinter": "", "PaperSize": "A4", "PrintMargins": {"Top": 20, "Bottom": 20, "Left": 20, "Right": 20}, "IncludeLogo": true, "IncludeQRCodes": true, "PrintCopies": 1}, "QRSettings": {"QRCodeSize": 100, "QRCodeQuality": "High", "IncludeProductInfo": true, "IncludeStoreInfo": true, "AutoGenerateQR": true}, "CameraSettings": {"DefaultCamera": 0, "ScanTimeout": 30, "AutoFocus": true, "Resolution": "640x480", "FrameRate": 30}, "ReportSettings": {"DefaultDateRange": "LastMonth", "IncludeCharts": true, "ExportFormat": "PDF", "AutoSaveReports": true, "ReportTemplate": "<PERSON><PERSON><PERSON>"}, "NotificationSettings": {"ShowLowStockAlerts": true, "ShowProfitWarnings": true, "ShowBackupReminders": true, "SoundEnabled": true, "PopupDuration": 5000}, "DatabaseSettings": {"ConnectionTimeout": 30, "CommandTimeout": 60, "EnableLogging": false, "AutoMigrate": true, "BackupBeforeUpdate": true}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}, "LogToFile": true, "LogFilePath": "Logs/app.log", "MaxLogFileSize": "10MB", "MaxLogFiles": 10}}