using System.Windows;
using DubaiGalleryAccounting.Database;
using Microsoft.EntityFrameworkCore;

namespace DubaiGalleryAccounting.Views
{
    public partial class InventoryWindow : Window
    {
        public InventoryWindow()
        {
            InitializeComponent();
            LoadInventory();
        }

        private async void LoadInventory()
        {
            using var context = new AppDbContext();
            var products = await context.Products.Where(p => p.IsActive).ToListAsync();
            InventoryGrid.ItemsSource = products;
        }
    }
}