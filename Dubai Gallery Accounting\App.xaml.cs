using System.Windows;
using DubaiGalleryAccounting.Database;
using Microsoft.EntityFrameworkCore;

namespace DubaiGalleryAccounting
{
    public partial class App : Application
    {
        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            // إنشاء قاعدة البيانات إذا لم تكن موجودة
            using (var context = new AppDbContext())
            {
                context.Database.EnsureCreated();
            }

            // تعيين اللغة العربية
            System.Threading.Thread.CurrentThread.CurrentUICulture = new System.Globalization.CultureInfo("ar-SA");
            System.Threading.Thread.CurrentThread.CurrentCulture = new System.Globalization.CultureInfo("ar-SA");
        }
    }
}