using System;
using System.Windows;
using DubaiGalleryAccounting.Database;

namespace DubaiGalleryAccounting
{
    public class Program
    {
        [STAThread]
        public static void Main(string[] args)
        {
            // التحقق من معاملات سطر الأوامر
            if (args.Length > 0 && args[0] == "--setup-db")
            {
                SetupDatabase();
                return;
            }

            // تشغيل التطبيق العادي
            var app = new App();
            app.InitializeComponent();
            app.Run();
        }

        private static void SetupDatabase()
        {
            try
            {
                using var context = new AppDbContext();
                context.Database.EnsureCreated();
                Console.WriteLine("تم إنشاء قاعدة البيانات بنجاح");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في إنشاء قاعدة البيانات: {ex.Message}");
            }
        }
    }
}