<Window x:Class="DubaiGalleryAccounting.Views.ProductsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة الأصناف - معرض دبي" 
        Height="700" Width="1000"
        Style="{StaticResource MainWindowStyle}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- شريط الأدوات -->
        <Border Grid.Row="0" Background="{StaticResource PrimaryBrush}" Padding="10">
            <StackPanel Orientation="Horizontal">
                <Button Content="➕ إضافة صنف جديد" 
                       Style="{StaticResource SecondaryButtonStyle}"
                       Click="AddProductButton_Click"/>
                <Button Content="✏️ تعديل" 
                       Style="{StaticResource SecondaryButtonStyle}"
                       Click="EditProductButton_Click"/>
                <Button Content="🗑️ حذف" 
                       Style="{StaticResource SecondaryButtonStyle}"
                       Click="DeleteProductButton_Click"/>
                <Button Content="🔄 تحديث" 
                       Style="{StaticResource SecondaryButtonStyle}"
                       Click="RefreshButton_Click"/>
                <Separator Margin="10,0"/>
                <TextBox x:Name="SearchTextBox" 
                        Style="{StaticResource TextBoxStyle}"
                        Width="200"
                        Text="البحث..."
                        GotFocus="SearchTextBox_GotFocus"
                        LostFocus="SearchTextBox_LostFocus"
                        TextChanged="SearchTextBox_TextChanged"/>
            </StackPanel>
        </Border>

        <!-- المحتوى الرئيسي -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- جدول الأصناف -->
            <DataGrid Grid.Column="0" 
                     x:Name="ProductsDataGrid"
                     Style="{StaticResource DataGridStyle}"
                     SelectionChanged="ProductsDataGrid_SelectionChanged">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="الرقم" Binding="{Binding Id}" Width="60"/>
                    <DataGridTextColumn Header="اسم الصنف" Binding="{Binding Name}" Width="*"/>
                    <DataGridTextColumn Header="النوع" Binding="{Binding Type}" Width="100"/>
                    <DataGridTextColumn Header="الماركة" Binding="{Binding Brand}" Width="100"/>
                    <DataGridTextColumn Header="الموديل" Binding="{Binding Model}" Width="100"/>
                    <DataGridTextColumn Header="IMEI" Binding="{Binding IMEI}" Width="120"/>
                    <DataGridTextColumn Header="سعر الشراء" Binding="{Binding PurchasePrice, StringFormat='{}{0:F2}'}" Width="100"/>
                    <DataGridTextColumn Header="سعر البيع" Binding="{Binding SalePrice, StringFormat='{}{0:F2}'}" Width="100"/>
                    <DataGridTextColumn Header="الكمية" Binding="{Binding Quantity}" Width="80"/>
                    <DataGridCheckBoxColumn Header="نشط" Binding="{Binding IsActive}" Width="60"/>
                </DataGrid.Columns>
            </DataGrid>

            <!-- تفاصيل الصنف -->
            <Border Grid.Column="1" Style="{StaticResource CardStyle}">
                <ScrollViewer>
                    <StackPanel x:Name="ProductDetailsPanel">
                        <TextBlock Text="تفاصيل الصنف" 
                                  FontSize="16" 
                                  FontWeight="Bold" 
                                  Margin="0,0,0,15"/>

                        <!-- صورة المنتج -->
                        <Border BorderBrush="#CCCCCC" 
                               BorderThickness="1" 
                               Width="150" 
                               Height="150" 
                               Margin="0,0,0,10">
                            <Image x:Name="ProductImage" 
                                  Stretch="Uniform"
                                  Source="/Resources/no-image.png"/>
                        </Border>

                        <Button Content="📷 تغيير الصورة" 
                               Style="{StaticResource PrimaryButtonStyle}"
                               Click="ChangeImageButton_Click"
                               Margin="0,0,0,15"/>

                        <!-- QR Code -->
                        <TextBlock Text="رمز QR:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <Border BorderBrush="#CCCCCC" 
                               BorderThickness="1" 
                               Width="120" 
                               Height="120" 
                               Margin="0,0,0,10">
                            <Image x:Name="QRCodeImage" 
                                  Stretch="Uniform"/>
                        </Border>

                        <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                            <Button Content="🔄 توليد QR" 
                                   Style="{StaticResource PrimaryButtonStyle}"
                                   Click="GenerateQRButton_Click"
                                   Margin="0,0,5,0"/>
                            <Button Content="💾 حفظ QR" 
                                   Style="{StaticResource SecondaryButtonStyle}"
                                   Click="SaveQRButton_Click"/>
                        </StackPanel>

                        <!-- معلومات إضافية -->
                        <TextBlock Text="معلومات إضافية:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBlock x:Name="ProductInfoText" 
                                  TextWrapping="Wrap" 
                                  Margin="0,0,0,10"/>

                        <!-- الملاحظات -->
                        <TextBlock Text="الملاحظات:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="NotesTextBox" 
                                Style="{StaticResource TextBoxStyle}"
                                Height="80" 
                                TextWrapping="Wrap" 
                                AcceptsReturn="True"
                                IsReadOnly="True"/>

                        <Button Content="💾 حفظ الملاحظات" 
                               Style="{StaticResource PrimaryButtonStyle}"
                               Click="SaveNotesButton_Click"
                               Margin="0,10,0,0"/>
                    </StackPanel>
                </ScrollViewer>
            </Border>
        </Grid>
    </Grid>
</Window>