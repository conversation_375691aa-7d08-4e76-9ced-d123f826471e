<Window x:Class="DubaiGalleryAccounting.Views.QRScanWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="مسح رمز QR - معرض دبي" 
        Height="400" Width="500"
        Style="{StaticResource MainWindowStyle}"
        WindowStartupLocation="CenterOwner">

    <Grid>
        <StackPanel Margin="20">
            <TextBlock Text="مسح رمز QR" FontSize="18" FontWeight="Bold" Margin="0,0,0,20"/>
            
            <Label Content="أدخل رمز IMEI يدوياً:" Style="{StaticResource LabelStyle}"/>
            <TextBox x:Name="IMEITextBox" Style="{StaticResource TextBoxStyle}"/>
            
            <TextBlock Text="أو" HorizontalAlignment="Center" Margin="0,10"/>
            
            <Button Content="📁 تحميل من ملف" 
                   Style="{StaticResource PrimaryButtonStyle}"
                   Click="LoadFromFileButton_Click"/>
            
            <TextBlock Text="البيانات المقروءة:" FontWeight="Bold" Margin="0,20,0,5"/>
            <TextBox x:Name="ScannedDataTextBox" 
                    Style="{StaticResource TextBoxStyle}"
                    Height="80" 
                    TextWrapping="Wrap" 
                    IsReadOnly="True"/>
            
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
                <Button Content="✅ استخدام" 
                       Style="{StaticResource PrimaryButtonStyle}"
                       Click="UseDataButton_Click"/>
                <Button Content="❌ إلغاء" 
                       Style="{StaticResource SecondaryButtonStyle}"
                       Click="CancelButton_Click"/>
            </StackPanel>
        </StackPanel>
    </Grid>
</Window>