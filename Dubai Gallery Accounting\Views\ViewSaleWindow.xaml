<Window x:Class="DubaiGalleryAccounting.Views.ViewSaleWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="عرض الفاتورة - معرض دبي" Height="500" Width="700"
        Style="{StaticResource MainWindowStyle}">
    <Grid>
        <StackPanel Margin="20">
            <TextBlock x:Name="InvoiceTitle" FontSize="18" FontWeight="Bold" Margin="0,0,0,20"/>
            <DataGrid x:Name="ItemsGrid" Style="{StaticResource DataGridStyle}" Height="300">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="المنتج" Binding="{Binding Product.Name}" Width="*"/>
                    <DataGridTextColumn Header="الكمية" Binding="{Binding Quantity}" Width="80"/>
                    <DataGridTextColumn Header="السعر" Binding="{Binding UnitPrice}" Width="100"/>
                    <DataGridTextColumn Header="المجموع" Binding="{Binding TotalPrice}" Width="100"/>
                </DataGrid.Columns>
            </DataGrid>
            <TextBlock x:Name="TotalText" FontSize="16" FontWeight="Bold" Margin="0,20,0,0"/>
        </StackPanel>
    </Grid>
</Window>