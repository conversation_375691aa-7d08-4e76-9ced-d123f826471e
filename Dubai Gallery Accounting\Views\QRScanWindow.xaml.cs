using System.Windows;
using Microsoft.Win32;

namespace DubaiGalleryAccounting.Views
{
    public partial class QRScanWindow : Window
    {
        public string? ScannedData { get; private set; }

        public QRScanWindow()
        {
            InitializeComponent();
        }

        private void LoadFromFileButton_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Filter = "Text files (*.txt)|*.txt|All files (*.*)|*.*",
                Title = "اختر ملف يحتوي على رمز IMEI"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                try
                {
                    var content = File.ReadAllText(openFileDialog.FileName);
                    ScannedDataTextBox.Text = content;
                    ScannedData = content.Trim();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في قراءة الملف: {ex.Message}", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void UseDataButton_Click(object sender, RoutedEventArgs e)
        {
            var manualInput = IMEITextBox.Text.Trim();
            var fileInput = ScannedDataTextBox.Text.Trim();

            if (!string.IsNullOrEmpty(manualInput))
            {
                ScannedData = manualInput;
            }
            else if (!string.IsNullOrEmpty(fileInput))
            {
                ScannedData = fileInput;
            }
            else
            {
                MessageBox.Show("يرجى إدخال رمز IMEI أو تحميل ملف", "تنبيه", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            DialogResult = true;
            Close();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}