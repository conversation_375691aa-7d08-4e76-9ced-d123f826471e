@echo off
chcp 65001 >nul
title معرض دبي - التثبيت المبسط

echo ========================================
echo    معرض دبي للموبايلات والكاميرات
echo        التثبيت المبسط والسريع
echo ========================================
echo.

echo [1/4] إنشاء المجلدات...
if not exist "Backups" mkdir "Backups"
if not exist "Reports" mkdir "Reports"
if not exist "Invoices" mkdir "Invoices"
if not exist "QRCodes" mkdir "QRCodes"
if not exist "Images" mkdir "Images"
echo ✓ تم إنشاء المجلدات

echo.
echo [2/4] التحقق من .NET...
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ .NET غير مثبت - يرجى تحميله من:
    echo https://dotnet.microsoft.com/download/dotnet/6.0
    pause
    exit /b 1
)
echo ✓ .NET متوفر

echo.
echo [3/4] تحميل المكتبات...
dotnet restore
if %errorlevel% neq 0 (
    echo ❌ فشل في تحميل المكتبات
    pause
    exit /b 1
)
echo ✓ تم تحميل المكتبات

echo.
echo [4/4] بناء البرنامج...
dotnet build --configuration Release
if %errorlevel% neq 0 (
    echo ❌ فشل في بناء البرنامج
    pause
    exit /b 1
)
echo ✓ تم بناء البرنامج

echo.
echo إنشاء ملف التشغيل...
echo @echo off > "تشغيل معرض دبي.bat"
echo chcp 65001 ^>nul >> "تشغيل معرض دبي.bat"
echo title معرض دبي >> "تشغيل معرض دبي.bat"
echo dotnet run --configuration Release >> "تشغيل معرض دبي.bat"
echo pause >> "تشغيل معرض دبي.bat"

echo.
echo ========================================
echo           تم التثبيت بنجاح! 🎉
echo ========================================
echo.
echo بيانات الدخول:
echo المستخدم: admin
echo كلمة المرور: admin123
echo.
echo للتشغيل: انقر على "تشغيل معرض دبي.bat"
echo.

set /p choice="هل تريد تشغيل البرنامج الآن؟ (Y/N): "
if /i "%choice%"=="Y" (
    start "" "تشغيل معرض دبي.bat"
)

echo.
pause