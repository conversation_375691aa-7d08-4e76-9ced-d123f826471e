using System.Windows;
using System.Windows.Controls;
using System.Windows.Media.Imaging;
using Microsoft.Win32;
using DubaiGalleryAccounting.Database;
using DubaiGalleryAccounting.Models;
using DubaiGalleryAccounting.Controllers;
using Microsoft.EntityFrameworkCore;

namespace DubaiGalleryAccounting.Views
{
    public partial class ProductsWindow : Window
    {
        private readonly AppDbContext _context;
        private readonly QRCodeService _qrCodeService;
        private Product? _selectedProduct;

        public ProductsWindow()
        {
            InitializeComponent();
            _context = new AppDbContext();
            _qrCodeService = new QRCodeService();
            LoadProducts();
        }

        private async void LoadProducts()
        {
            try
            {
                var products = await _context.Products
                    .Where(p => p.IsActive)
                    .OrderBy(p => p.Name)
                    .ToListAsync();

                ProductsDataGrid.ItemsSource = products;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الأصناف: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ProductsDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            _selectedProduct = ProductsDataGrid.SelectedItem as Product;
            UpdateProductDetails();
        }

        private void UpdateProductDetails()
        {
            if (_selectedProduct == null)
            {
                ProductDetailsPanel.IsEnabled = false;
                return;
            }

            ProductDetailsPanel.IsEnabled = true;

            // تحديث صورة المنتج
            if (!string.IsNullOrEmpty(_selectedProduct.ImagePath) && File.Exists(_selectedProduct.ImagePath))
            {
                ProductImage.Source = new BitmapImage(new Uri(_selectedProduct.ImagePath));
            }
            else
            {
                ProductImage.Source = null;
            }

            // تحديث QR Code
            if (!string.IsNullOrEmpty(_selectedProduct.QRCodePath) && File.Exists(_selectedProduct.QRCodePath))
            {
                QRCodeImage.Source = new BitmapImage(new Uri(_selectedProduct.QRCodePath));
            }
            else
            {
                QRCodeImage.Source = null;
            }

            // تحديث المعلومات
            ProductInfoText.Text = $"تاريخ الإضافة: {_selectedProduct.CreatedDate:yyyy/MM/dd}\n" +
                                  $"آخر تعديل: {_selectedProduct.LastModified?.ToString("yyyy/MM/dd") ?? "لا يوجد"}\n" +
                                  $"الربح المتوقع: {(_selectedProduct.SalePrice - _selectedProduct.PurchasePrice):F2} ريال";

            NotesTextBox.Text = _selectedProduct.Notes;
            NotesTextBox.IsReadOnly = false;
        }

        private void AddProductButton_Click(object sender, RoutedEventArgs e)
        {
            var addProductWindow = new AddEditProductWindow();
            if (addProductWindow.ShowDialog() == true)
            {
                LoadProducts();
            }
        }

        private void EditProductButton_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedProduct == null)
            {
                MessageBox.Show("يرجى اختيار صنف للتعديل", "تنبيه", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var editProductWindow = new AddEditProductWindow(_selectedProduct);
            if (editProductWindow.ShowDialog() == true)
            {
                LoadProducts();
                UpdateProductDetails();
            }
        }

        private async void DeleteProductButton_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedProduct == null)
            {
                MessageBox.Show("يرجى اختيار صنف للحذف", "تنبيه", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var result = MessageBox.Show($"هل تريد حذف الصنف '{_selectedProduct.Name}'؟\nسيتم إلغاء تفعيله فقط وليس حذفه نهائياً.", 
                "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    _selectedProduct.IsActive = false;
                    _selectedProduct.LastModified = DateTime.Now;
                    await _context.SaveChangesAsync();

                    MessageBox.Show("تم إلغاء تفعيل الصنف بنجاح", "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    
                    LoadProducts();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف الصنف: {ex.Message}", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadProducts();
        }

        private void SearchTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (SearchTextBox.Text == "البحث...")
            {
                SearchTextBox.Text = "";
            }
        }

        private void SearchTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(SearchTextBox.Text))
            {
                SearchTextBox.Text = "البحث...";
            }
        }

        private async void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (SearchTextBox.Text == "البحث..." || string.IsNullOrWhiteSpace(SearchTextBox.Text))
            {
                LoadProducts();
                return;
            }

            try
            {
                var searchTerm = SearchTextBox.Text.ToLower();
                var products = await _context.Products
                    .Where(p => p.IsActive && 
                               (p.Name.ToLower().Contains(searchTerm) ||
                                p.IMEI.ToLower().Contains(searchTerm) ||
                                p.Brand.ToLower().Contains(searchTerm) ||
                                p.Model.ToLower().Contains(searchTerm)))
                    .OrderBy(p => p.Name)
                    .ToListAsync();

                ProductsDataGrid.ItemsSource = products;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ChangeImageButton_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedProduct == null) return;

            var openFileDialog = new OpenFileDialog
            {
                Filter = "Image files (*.jpg, *.jpeg, *.png, *.bmp)|*.jpg;*.jpeg;*.png;*.bmp",
                Title = "اختر صورة المنتج"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                try
                {
                    var imagesPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Images");
                    if (!Directory.Exists(imagesPath))
                        Directory.CreateDirectory(imagesPath);

                    var fileName = $"Product_{_selectedProduct.Id}_{DateTime.Now:yyyyMMddHHmmss}{Path.GetExtension(openFileDialog.FileName)}";
                    var newPath = Path.Combine(imagesPath, fileName);

                    File.Copy(openFileDialog.FileName, newPath, true);

                    _selectedProduct.ImagePath = newPath;
                    _selectedProduct.LastModified = DateTime.Now;
                    _context.SaveChanges();

                    UpdateProductDetails();
                    MessageBox.Show("تم تحديث صورة المنتج بنجاح", "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في تحديث الصورة: {ex.Message}", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private async void GenerateQRButton_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedProduct == null) return;

            try
            {
                var qrPath = _qrCodeService.GenerateQRCode(_selectedProduct.IMEI, _selectedProduct.Name);
                
                _selectedProduct.QRCodePath = qrPath;
                _selectedProduct.LastModified = DateTime.Now;
                await _context.SaveChangesAsync();

                UpdateProductDetails();
                MessageBox.Show("تم توليد رمز QR بنجاح", "نجح", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في توليد رمز QR: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SaveQRButton_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedProduct == null || string.IsNullOrEmpty(_selectedProduct.QRCodePath)) return;

            var saveFileDialog = new SaveFileDialog
            {
                Filter = "PNG files (*.png)|*.png",
                FileName = $"QR_{_selectedProduct.IMEI}.png",
                Title = "حفظ رمز QR"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                try
                {
                    File.Copy(_selectedProduct.QRCodePath, saveFileDialog.FileName, true);
                    MessageBox.Show("تم حفظ رمز QR بنجاح", "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حفظ رمز QR: {ex.Message}", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private async void SaveNotesButton_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedProduct == null) return;

            try
            {
                _selectedProduct.Notes = NotesTextBox.Text;
                _selectedProduct.LastModified = DateTime.Now;
                await _context.SaveChangesAsync();

                MessageBox.Show("تم حفظ الملاحظات بنجاح", "نجح", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الملاحظات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            _context?.Dispose();
            base.OnClosed(e);
        }
    }
}