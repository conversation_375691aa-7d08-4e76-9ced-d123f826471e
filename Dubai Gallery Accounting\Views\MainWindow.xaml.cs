using System.Windows;
using System.Windows.Threading;
using DubaiGalleryAccounting.Database;
using DubaiGalleryAccounting.Controllers;
using LiveCharts;
using LiveCharts.Wpf;

namespace DubaiGalleryAccounting.Views
{
    public partial class MainWindow : Window
    {
        private readonly DispatcherTimer _timer;
        private readonly ReportService _reportService;

        public MainWindow()
        {
            InitializeComponent();
            
            _reportService = new ReportService(new AppDbContext());
            
            // إعداد المؤقت لتحديث الوقت
            _timer = new DispatcherTimer();
            _timer.Interval = TimeSpan.FromSeconds(1);
            _timer.Tick += Timer_Tick;
            _timer.Start();

            // تحديث معلومات المستخدم
            UpdateUserInfo();
            
            // تحميل بيانات لوحة المعلومات
            LoadDashboardData();
        }

        private void Timer_Tick(object sender, EventArgs e)
        {
            DateTimeText.Text = DateTime.Now.ToString("yyyy/MM/dd - HH:mm:ss");
        }

        private void UpdateUserInfo()
        {
            if (LoginWindow.CurrentUser != null)
            {
                UserInfoText.Text = $"مرحباً، {LoginWindow.CurrentUser.FullName} ({LoginWindow.CurrentUser.Role})";
            }
        }

        private async void LoadDashboardData()
        {
            try
            {
                StatusText.Text = "جاري تحميل البيانات...";
                
                var dashboardData = await _reportService.GetDashboardDataAsync();
                
                // تحديث بطاقات الإحصائيات
                TotalSalesText.Text = $"{dashboardData.TotalSales:F2} ريال";
                TotalProfitText.Text = $"{dashboardData.TotalProfit:F2} ريال";
                LowStockText.Text = dashboardData.LowStockCount.ToString();
                TotalSalesCountText.Text = dashboardData.TotalSalesCount.ToString();

                // تحديث الرسم البياني
                var salesValues = new ChartValues<decimal>();
                var labels = new List<string>();

                foreach (var monthData in dashboardData.MonthlySalesData.TakeLast(12))
                {
                    salesValues.Add(monthData.Sales);
                    labels.Add(monthData.Month);
                }

                SalesChart.Series = new SeriesCollection
                {
                    new LineSeries
                    {
                        Title = "المبيعات",
                        Values = salesValues,
                        PointGeometry = DefaultGeometries.Circle,
                        PointGeometrySize = 8
                    }
                };

                SalesChart.AxisX.Clear();
                SalesChart.AxisX.Add(new Axis
                {
                    Title = "الشهر",
                    Labels = labels
                });

                // تحديث جدول أعلى المنتجات
                TopProductsGrid.ItemsSource = dashboardData.TopProducts;

                StatusText.Text = "تم تحميل البيانات بنجاح";
            }
            catch (Exception ex)
            {
                StatusText.Text = $"خطأ في تحميل البيانات: {ex.Message}";
            }
        }

        private void LogoutButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل تريد تسجيل الخروج؟", "تأكيد", 
                MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                _timer.Stop();
                var loginWindow = new LoginWindow();
                loginWindow.Show();
                this.Close();
            }
        }

        private void DashboardButton_Click(object sender, RoutedEventArgs e)
        {
            // إظهار لوحة المعلومات
            DashboardPanel.Visibility = Visibility.Visible;
            LoadDashboardData();
        }

        private void ProductsButton_Click(object sender, RoutedEventArgs e)
        {
            var productsWindow = new ProductsWindow();
            productsWindow.ShowDialog();
        }

        private void PurchasesButton_Click(object sender, RoutedEventArgs e)
        {
            var purchasesWindow = new PurchasesWindow();
            purchasesWindow.ShowDialog();
        }

        private void SalesButton_Click(object sender, RoutedEventArgs e)
        {
            var salesWindow = new SalesWindow();
            salesWindow.ShowDialog();
            LoadDashboardData(); // تحديث البيانات بعد المبيعات
        }

        private void InventoryButton_Click(object sender, RoutedEventArgs e)
        {
            var inventoryWindow = new InventoryWindow();
            inventoryWindow.ShowDialog();
        }

        private void ExpensesButton_Click(object sender, RoutedEventArgs e)
        {
            var expensesWindow = new ExpensesWindow();
            expensesWindow.ShowDialog();
        }

        private void ReportsButton_Click(object sender, RoutedEventArgs e)
        {
            var reportsWindow = new ReportsWindow();
            reportsWindow.ShowDialog();
        }

        private void QRScanButton_Click(object sender, RoutedEventArgs e)
        {
            var qrScanWindow = new QRScanWindow();
            qrScanWindow.ShowDialog();
        }

        private void SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            var settingsWindow = new SettingsWindow();
            settingsWindow.ShowDialog();
        }

        protected override void OnClosed(EventArgs e)
        {
            _timer?.Stop();
            base.OnClosed(e);
        }
    }
}