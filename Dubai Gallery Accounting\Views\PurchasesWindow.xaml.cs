using System.Windows;
using DubaiGalleryAccounting.Database;
using Microsoft.EntityFrameworkCore;

namespace DubaiGalleryAccounting.Views
{
    public partial class PurchasesWindow : Window
    {
        public PurchasesWindow()
        {
            InitializeComponent();
            LoadPurchases();
        }

        private async void LoadPurchases()
        {
            using var context = new AppDbContext();
            var purchases = await context.Purchases.ToListAsync();
            PurchasesGrid.ItemsSource = purchases;
        }
    }
}