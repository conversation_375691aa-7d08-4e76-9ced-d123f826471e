<Window x:Class="DubaiGalleryAccounting.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:lvc="clr-namespace:LiveCharts.Wpf;assembly=LiveCharts.Wpf"
        Title="معرض دبي - نظام المحاسبة" 
        Height="800" Width="1200"
        Style="{StaticResource MainWindowStyle}"
        WindowState="Maximized">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- شريط القوائم العلوي -->
        <Border Grid.Row="0" Background="{StaticResource PrimaryBrush}" Padding="10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="معرض دبي - نظام المحاسبة" 
                              Foreground="White" 
                              FontSize="18" 
                              FontWeight="Bold" 
                              VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock x:Name="UserInfoText" 
                              Foreground="White" 
                              VerticalAlignment="Center" 
                              Margin="0,0,20,0"/>
                    <Button Content="تسجيل الخروج" 
                           Style="{StaticResource SecondaryButtonStyle}"
                           Click="LogoutButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- المحتوى الرئيسي -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- القائمة الجانبية -->
            <Border Grid.Column="0" Background="White" BorderBrush="#E0E0E0" BorderThickness="0,0,1,0">
                <ScrollViewer>
                    <StackPanel Margin="10">
                        <Button Content="🏠 الرئيسية" 
                               Style="{StaticResource PrimaryButtonStyle}"
                               HorizontalAlignment="Stretch"
                               Click="DashboardButton_Click"/>
                        
                        <Separator Margin="0,10"/>
                        
                        <Button Content="📦 إدارة الأصناف" 
                               Style="{StaticResource PrimaryButtonStyle}"
                               HorizontalAlignment="Stretch"
                               Click="ProductsButton_Click"/>
                        
                        <Button Content="📥 المشتريات" 
                               Style="{StaticResource PrimaryButtonStyle}"
                               HorizontalAlignment="Stretch"
                               Click="PurchasesButton_Click"/>
                        
                        <Button Content="📤 المبيعات" 
                               Style="{StaticResource PrimaryButtonStyle}"
                               HorizontalAlignment="Stretch"
                               Click="SalesButton_Click"/>
                        
                        <Button Content="🏪 المخزن" 
                               Style="{StaticResource PrimaryButtonStyle}"
                               HorizontalAlignment="Stretch"
                               Click="InventoryButton_Click"/>
                        
                        <Button Content="💸 المصروفات" 
                               Style="{StaticResource PrimaryButtonStyle}"
                               HorizontalAlignment="Stretch"
                               Click="ExpensesButton_Click"/>
                        
                        <Separator Margin="0,10"/>
                        
                        <Button Content="📊 التقارير" 
                               Style="{StaticResource SecondaryButtonStyle}"
                               HorizontalAlignment="Stretch"
                               Click="ReportsButton_Click"/>
                        
                        <Button Content="📷 مسح QR" 
                               Style="{StaticResource SecondaryButtonStyle}"
                               HorizontalAlignment="Stretch"
                               Click="QRScanButton_Click"/>
                        
                        <Separator Margin="0,10"/>
                        
                        <Button Content="⚙️ الإعدادات" 
                               Style="{StaticResource SecondaryButtonStyle}"
                               HorizontalAlignment="Stretch"
                               Click="SettingsButton_Click"/>
                    </StackPanel>
                </ScrollViewer>
            </Border>

            <!-- منطقة المحتوى -->
            <Grid Grid.Column="1" x:Name="ContentArea">
                <!-- لوحة المعلومات الافتراضية -->
                <ScrollViewer x:Name="DashboardPanel">
                    <StackPanel Margin="20">
                        <TextBlock Text="لوحة المعلومات" 
                                  FontSize="24" 
                                  FontWeight="Bold" 
                                  Foreground="{StaticResource PrimaryBrush}"
                                  Margin="0,0,0,20"/>

                        <!-- بطاقات الإحصائيات -->
                        <UniformGrid Columns="4" Margin="0,0,0,20">
                            <Border Style="{StaticResource StatCardStyle}">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="💰" FontSize="30" HorizontalAlignment="Center"/>
                                    <TextBlock x:Name="TotalSalesText" 
                                              Text="0 ريال" 
                                              FontSize="18" 
                                              FontWeight="Bold" 
                                              HorizontalAlignment="Center"/>
                                    <TextBlock Text="إجمالي المبيعات" 
                                              FontSize="12" 
                                              Foreground="Gray" 
                                              HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Border>

                            <Border Style="{StaticResource StatCardStyle}">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="📈" FontSize="30" HorizontalAlignment="Center"/>
                                    <TextBlock x:Name="TotalProfitText" 
                                              Text="0 ريال" 
                                              FontSize="18" 
                                              FontWeight="Bold" 
                                              HorizontalAlignment="Center"/>
                                    <TextBlock Text="إجمالي الأرباح" 
                                              FontSize="12" 
                                              Foreground="Gray" 
                                              HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Border>

                            <Border Style="{StaticResource StatCardStyle}">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="📦" FontSize="30" HorizontalAlignment="Center"/>
                                    <TextBlock x:Name="LowStockText" 
                                              Text="0" 
                                              FontSize="18" 
                                              FontWeight="Bold" 
                                              HorizontalAlignment="Center"/>
                                    <TextBlock Text="أصناف منخفضة" 
                                              FontSize="12" 
                                              Foreground="Gray" 
                                              HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Border>

                            <Border Style="{StaticResource StatCardStyle}">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="🛒" FontSize="30" HorizontalAlignment="Center"/>
                                    <TextBlock x:Name="TotalSalesCountText" 
                                              Text="0" 
                                              FontSize="18" 
                                              FontWeight="Bold" 
                                              HorizontalAlignment="Center"/>
                                    <TextBlock Text="عدد المبيعات" 
                                              FontSize="12" 
                                              Foreground="Gray" 
                                              HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Border>
                        </UniformGrid>

                        <!-- الرسم البياني -->
                        <Border Style="{StaticResource CardStyle}">
                            <StackPanel>
                                <TextBlock Text="مبيعات آخر 12 شهر" 
                                          FontSize="16" 
                                          FontWeight="Bold" 
                                          Margin="0,0,0,10"/>
                                <lvc:CartesianChart x:Name="SalesChart" Height="300">
                                    <lvc:CartesianChart.AxisX>
                                        <lvc:Axis Title="الشهر"/>
                                    </lvc:CartesianChart.AxisX>
                                    <lvc:CartesianChart.AxisY>
                                        <lvc:Axis Title="المبلغ (ريال)"/>
                                    </lvc:CartesianChart.AxisY>
                                </lvc:CartesianChart>
                            </StackPanel>
                        </Border>

                        <!-- أعلى المنتجات مبيعاً -->
                        <Border Style="{StaticResource CardStyle}">
                            <StackPanel>
                                <TextBlock Text="أعلى المنتجات مبيعاً" 
                                          FontSize="16" 
                                          FontWeight="Bold" 
                                          Margin="0,0,0,10"/>
                                <DataGrid x:Name="TopProductsGrid" 
                                         Style="{StaticResource DataGridStyle}"
                                         Height="200">
                                    <DataGrid.Columns>
                                        <DataGridTextColumn Header="اسم المنتج" Binding="{Binding ProductName}" Width="*"/>
                                        <DataGridTextColumn Header="الكمية المباعة" Binding="{Binding QuantitySold}" Width="100"/>
                                        <DataGridTextColumn Header="إجمالي الإيرادات" Binding="{Binding TotalRevenue, StringFormat='{}{0:F2} ريال'}" Width="150"/>
                                    </DataGrid.Columns>
                                </DataGrid>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </ScrollViewer>
            </Grid>
        </Grid>

        <!-- شريط الحالة -->
        <Border Grid.Row="2" Background="#F0F0F0" BorderBrush="#E0E0E0" BorderThickness="0,1,0,0" Padding="10,5">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock x:Name="StatusText" 
                          Text="جاهز" 
                          VerticalAlignment="Center"/>
                
                <TextBlock Grid.Column="1" 
                          x:Name="DateTimeText" 
                          VerticalAlignment="Center"/>
            </Grid>
        </Border>
    </Grid>
</Window>